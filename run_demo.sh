#!/bin/bash

# 一键演示脚本 - Brand Reviews API
# 自动初始化数据库、启动服务、运行演示

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
API_PORT=9091
DB_NAME="brandreviews"
DB_USER="postgres"
DB_PASSWORD="postgres"

print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                                                                              ║"
    echo "║                    🚀 Brand Reviews API 一键演示                            ║"
    echo "║                                                                              ║"
    echo "║                     企业级高性能API系统演示                                  ║"
    echo "║                                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}"
    echo ""
}

print_success() {
    echo -e "${GRE<PERSON>}✅ $1${NC}"
    echo ""
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
    echo ""
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    echo ""
}

check_dependencies() {
    print_step "检查依赖环境..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.21+"
        exit 1
    fi
    print_success "Go 环境正常: $(go version)"
    
    # 检查 PostgreSQL
    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL 未安装，请先安装 PostgreSQL 13+"
        exit 1
    fi
    print_success "PostgreSQL 环境正常: $(psql --version)"
    
    # 检查 PostgreSQL 服务
    if ! pg_isready -h localhost -p 5432 -U "$DB_USER" > /dev/null 2>&1; then
        print_error "PostgreSQL 服务未运行，请启动 PostgreSQL 服务"
        exit 1
    fi
    print_success "PostgreSQL 服务正常运行"
    
    # 检查端口占用
    if lsof -i :$API_PORT > /dev/null 2>&1; then
        print_warning "端口 $API_PORT 已被占用，将尝试终止现有进程"
        pkill -f "go run cmd/api/main.go cmd/api/wire_gen.go" || true
        sleep 2
    fi
}

setup_environment() {
    print_step "设置环境..."
    
    # 创建配置文件
    if [ ! -f "config.yaml" ]; then
        if [ -f "config.example.yaml" ]; then
            cp config.example.yaml config.yaml
            print_success "配置文件创建成功"
        else
            print_warning "配置文件模板不存在，使用默认配置"
        fi
    else
        print_success "配置文件已存在"
    fi
    
    # 安装 Go 依赖
    print_step "安装 Go 依赖..."
    go mod tidy
    print_success "依赖安装完成"
}

init_database() {
    print_step "初始化数据库..."
    
    if [ -f "scripts/init_database.sh" ]; then
        export DB_NAME="$DB_NAME"
        export DB_USER="$DB_USER"
        export DB_PASSWORD="$DB_PASSWORD"
        
        ./scripts/init_database.sh
        print_success "数据库初始化完成"
    else
        print_error "数据库初始化脚本不存在"
        exit 1
    fi
}

start_api_server() {
    print_step "启动API服务..."
    
    # 在后台启动服务
    nohup go run cmd/api/main.go > api.log 2>&1 &
    API_PID=$!
    
    # 等待服务启动
    echo "等待服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:$API_PORT/health > /dev/null 2>&1; then
            print_success "API服务启动成功 (PID: $API_PID)"
            echo "服务地址: http://localhost:$API_PORT"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    print_error "API服务启动失败"
    echo "查看日志: tail -f api.log"
    exit 1
}

run_demo() {
    print_step "运行API演示..."
    
    echo -e "${YELLOW}演示将展示以下功能：${NC}"
    echo "🏥 健康检查和系统监控"
    echo "📂 分类和标签管理"
    echo "🏢 品牌信息查询"
    echo "📝 文章内容管理"
    echo "🎯 优惠活动展示"
    echo "🎫 优惠券管理"
    echo "⚡ 性能和缓存效果"
    echo ""
    
    read -p "按回车键开始演示..." -r
    echo ""
    
    if [ -f "scripts/demo_api_results.sh" ]; then
        ./scripts/demo_api_results.sh
    else
        print_error "演示脚本不存在"
        exit 1
    fi
}

cleanup() {
    print_step "清理资源..."
    
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null || true
        print_success "API服务已停止"
    fi
    
    # 清理日志文件
    rm -f api.log nohup.out
}

show_summary() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC}                              🎉 演示完成！                                   ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${GREEN}✅ 演示成功展示了以下特性：${NC}"
    echo ""
    echo -e "${CYAN}🚀 性能优化：${NC}"
    echo "   • 多级缓存系统 (L1/L2)"
    echo "   • 内存对齐优化"
    echo "   • Gzip压缩传输"
    echo "   • 数据库连接池"
    echo ""
    echo -e "${CYAN}🛡️ 可靠性保障：${NC}"
    echo "   • 熔断器保护"
    echo "   • 限流机制"
    echo "   • 优雅关闭"
    echo "   • 错误恢复"
    echo ""
    echo -e "${CYAN}📊 可观测性：${NC}"
    echo "   • 健康检查"
    echo "   • 指标监控"
    echo "   • 请求追踪"
    echo "   • 结构化日志"
    echo ""
    echo -e "${CYAN}🔒 安全特性：${NC}"
    echo "   • 输入验证"
    echo "   • XSS防护"
    echo "   • 参数清理"
    echo "   • 错误处理"
    echo ""
    echo -e "${YELLOW}📚 相关资源：${NC}"
    echo "   • API文档: docs/API_DOCUMENTATION.md"
    echo "   • 演示指南: README_DEMO.md"
    echo "   • 配置示例: config.example.yaml"
    echo ""
    echo -e "${BLUE}🔗 快速链接：${NC}"
    echo "   • 健康检查: http://localhost:$API_PORT/health"
    echo "   • 品牌列表: http://localhost:$API_PORT/api/v1/brand"
    echo "   • 文章列表: http://localhost:$API_PORT/api/v1/article"
    echo "   • 优惠列表: http://localhost:$API_PORT/api/v1/deal"
    echo "   • 优惠券列表: http://localhost:$API_PORT/api/v1/coupon"
    echo ""
}

main() {
    print_banner
    
    # 设置清理函数
    trap cleanup EXIT
    
    # 执行步骤
    check_dependencies
    setup_environment
    init_database
    start_api_server
    run_demo
    
    # 显示总结
    show_summary
    
    # 询问是否保持服务运行
    echo -e "${YELLOW}是否保持API服务运行？(y/n): ${NC}"
    read -r keep_running
    
    if [[ $keep_running =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}API服务将继续运行在后台${NC}"
        echo "PID: $API_PID"
        echo "停止服务: kill $API_PID"
        echo "查看日志: tail -f api.log"
        
        # 取消清理函数，保持服务运行
        trap - EXIT
    else
        echo -e "${BLUE}正在停止服务...${NC}"
    fi
}

# 检查是否在正确的目录
if [ ! -f "go.mod" ]; then
    print_error "请在项目根目录运行此脚本"
    exit 1
fi

# 运行主函数
main "$@"
