package service

import (
	"brandreviews/domain/article/entity"
	"brandreviews/domain/article/repository"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ArticleService interface {
	// CreateArticle 创建博客
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	// UpdateArticle 更新博客
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	// DeleteArticle 删除博客
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
	// GetArticleDetailById 根据ID查找博客
	GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error)
	// GetArticleDetailBySlug 根据Slug查找博客
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error)
	// GetArticleListByCondition 搜索博客
	GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
}

type ArticleServiceImpl struct {
	repo   repository.ArticleRepository
	logger *zap.Logger
}

func NewArticleService(repo repository.ArticleRepository, logger *zap.Logger) ArticleService {
	return &ArticleServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

func (s *ArticleServiceImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	// 创建 Article
	err := s.repo.CreateArticle(ctx, article)
	if err != nil {
		s.logger.Error("Failed to create article",
			zap.Any("article", article),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *ArticleServiceImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	// 更新商家
	err := s.repo.UpdateArticle(ctx, article)
	if err != nil {
		s.logger.Error("Failed to update article",
			zap.Any("article", article),
			zap.Error(err),
		)
		return err
	}
	return nil
}

func (s *ArticleServiceImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	// 删除商家
	err := s.repo.DeleteArticle(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete article",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return err
	}

	return nil
}

func (s *ArticleServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error) {
	article, err := s.repo.GetArticleDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get article from database",
			zap.Uint64("id", id),
			zap.Error(err),
		)
		return nil, err
	}
	return article, nil
}

func (s *ArticleServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error) {
	article, err := s.repo.GetArticleDetailBySlug(ctx, slug)
	if err != nil {
		s.logger.Error("Failed to get article from database",
			zap.String("slug", slug),
			zap.Error(err),
		)
		return nil, err
	}
	return article, nil
}

func (s *ArticleServiceImpl) GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error) {
	article, total, err := s.repo.GetArticleListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get articles from database",
			zap.Any("condition", condition),
			zap.Error(err),
		)
		return nil, 0, err
	}
	return article, total, nil
}

func (s *ArticleServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	// 更新商家状态
	total, err := s.repo.GetArticleCount(ctx)
	if err != nil {
		s.logger.Error("Failed to get article count",
			zap.Error(err),
		)
		return 0, err
	}

	return total, nil
}
