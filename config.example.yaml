# Brand Reviews API 配置文件示例
# 复制此文件为 config.yaml 并根据实际环境修改配置

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release, test
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "brandreviews"
  sslmode: "disable"
  timezone: "Asia/Shanghai"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600s
  conn_max_idle_time: 1800s

# 日志配置
logger:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, console
  output: "stdout"  # stdout, stderr, file path
  max_size: 100  # MB
  max_backups: 5
  max_age: 30  # days
  compress: true

# 缓存配置
cache:
  default_ttl: "15m"
  cleanup_interval: "5m"
  max_items: 10000
  l1_ttl: "5m"
  l1_max_items: 5000
  l2_ttl: "1h"
  l2_max_items: 20000

# 限流配置
rate_limit:
  requests_per_second: 100
  burst_size: 200
  cleanup_interval: "1h"

# 熔断器配置
circuit_breaker:
  failure_threshold: 5
  success_threshold: 3
  timeout: "60s"
  max_requests: 10
  interval: "60s"

# 压缩配置
compression:
  level: 6  # 1-9, 6 is default
  min_size: 1024  # bytes
  excluded_paths:
    - "/health"
    - "/metrics"
  excluded_mime_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "video/mp4"
    - "application/zip"

# 监控配置
monitoring:
  enable_metrics: true
  enable_tracing: true
  enable_health_checks: true
  metrics_path: "/metrics"
  health_path: "/health"

# 安全配置
security:
  enable_cors: true
  cors_origins:
    - "http://localhost:3000"
    - "https://yourdomain.com"
  cors_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  cors_headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Requested-With"

# 应用配置
app:
  name: "Brand Reviews API"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true
  timezone: "Asia/Shanghai"
  default_page_size: 30
  max_page_size: 100

# 第三方服务配置（如果需要）
external_services:
  # 示例：邮件服务
  email:
    provider: "smtp"
    host: "smtp.gmail.com"
    port: 587
    username: "<EMAIL>"
    password: "your-password"
    from: "<EMAIL>"
  
  # 示例：文件存储
  storage:
    provider: "local"  # local, s3, oss
    local_path: "./uploads"
    base_url: "http://localhost:8080/uploads"

# 功能开关
features:
  enable_caching: true
  enable_rate_limiting: true
  enable_circuit_breaker: true
  enable_compression: true
  enable_request_tracing: true
  enable_metrics: true
  enable_health_checks: true
  enable_graceful_shutdown: true
