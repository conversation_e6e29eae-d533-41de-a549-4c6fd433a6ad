//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	brandapp "brandreviews/application/brand/appservice"
	couponapp "brandreviews/application/coupon/appservice"
	"brandreviews/application/task/scheduler"
	"brandreviews/application/task/service"
	"brandreviews/config"
	brandservice "brandreviews/domain/brand/service"
	couponservice "brandreviews/domain/coupon/service"
	"brandreviews/infra/dao"
	"brandreviews/infra/database"

	"github.com/google/wire"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ProviderSet 定义所有的依赖提供者
var ProviderSet = wire.NewSet(
	// 基础设施
	ProvidePostgres,
	ProvideLogger,

	// 数据访问层
	dao.NewCouponPostgresRepository,
	dao.NewBrandPostgresRepository,

	// 领域服务层
	couponservice.NewCouponService,
	brandservice.NewBrandService,

	// 应用服务层
	couponapp.NewCouponAppService,
	brandapp.NewBrandAppService,

	// 任务服务层
	service.NewSyncService,

	// 调度器
	scheduler.NewScheduler,
)

type TaskApplication struct {
	Scheduler  scheduler.Scheduler
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
}

// InitializeTaskApplication 初始化任务应用程序
func InitializeTaskApplication(cfg *config.Config) (*TaskApplication, error) {
	wire.Build(
		ProviderSet,
		wire.Struct(new(TaskApplication), "*"),
	)
	return nil, nil
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideLogger 提供日志记录器
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.Logger.Level == "debug" {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return nil, err
	}

	return logger, nil
}
