package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"

	"brandreviews/config"

	"github.com/joho/godotenv"
)

func main() {
	// 获取环境配置的优先级：
	// 1. 命令行参数
	// 2. 环境变量
	// 3. .env 文件
	// 4. 默认值

	var env string
	flag.StringVar(&env, "env", "", "environment (local, test, live)")
	flag.Parse()

	// 如果没有通过命令行指定环境，则尝试从环境变量获取
	if env == "" {
		env = os.Getenv("APP_ENV")
	}

	// 如果环境变量也没有设置，则尝试加载 .env 文件
	if env == "" {
		// 获取项目根目录
		rootDir := findProjectRoot()

		// 加载 env 文件
		if err := godotenv.Load(filepath.Join(rootDir, "env")); err != nil {
			log.Printf("Warning: Error loading env file: %v", err)
		}

		env = os.Getenv("APP_ENV")
	}

	// 如果所有方式都没有设置环境，则使用默认值
	if env == "" {
		env = "local"
	}

	// 加载配置
	cfg, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 使用 Wire 初始化任务应用程序
	app, err := InitializeTaskApplication(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize task application: %v", err)
	}
	defer app.Logger.Sync()

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动调度器
	if err := app.Scheduler.Start(ctx); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	// 设置信号处理
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	log.Printf("Task service started successfully")

	// 等待信号
	<-signalChan
	log.Printf("Received shutdown signal, stopping task service...")

	// 停止调度器
	if err := app.Scheduler.Stop(); err != nil {
		log.Printf("Error stopping scheduler: %v", err)
	}

	log.Printf("Task service stopped")
}

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 从当前目录开始向上查找，直到找到包含 .env 文件的目录
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "env")); err == nil {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			return ""
		}
		dir = parent
	}
}
