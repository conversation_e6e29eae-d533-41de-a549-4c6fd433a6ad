package router

import (
	"brandreviews/interfaces/api/handler/article"
	"brandreviews/interfaces/api/handler/brand"
	"brandreviews/interfaces/api/handler/category"
	"brandreviews/interfaces/api/handler/coupon"
	"brandreviews/interfaces/api/handler/deal"
	"brandreviews/interfaces/api/handler/health"
	"brandreviews/interfaces/api/handler/tag"
	"brandreviews/interfaces/api/middleware"

	"github.com/gin-gonic/gin"
)

// ApiServer HTTP服务器
type ApiServer struct {
	tagHandler      *tag.TagHandler
	categoryHandler *category.CategoryHandler
	brandHandler    *brand.BrandHandler
	dealHandler     *deal.DealHandler
	couponHandler   *coupon.CouponHandler
	articleHandler  *article.ArticleHandler
	healthHandler   *health.HealthHandler
}

// NewApiServer 创建API服务器
func NewApiServer(
	tagHandler *tag.TagHandler,
	categoryHandler *category.CategoryHandler,
	brandHandler *brand.BrandHandler,
	dealHandler *deal.Deal<PERSON><PERSON><PERSON>,
	couponHandler *coupon.CouponHandler,
	articleHandler *article.ArticleHandler,
	healthHandler *health.HealthHandler,
) *ApiServer {
	return &ApiServer{
		tagHandler:      tagHandler,
		categoryHandler: categoryHandler,
		brandHandler:    brandHandler,
		dealHandler:     dealHandler,
		couponHandler:   couponHandler,
		articleHandler:  articleHandler,
		healthHandler:   healthHandler,
	}
}

// setupPublicRoutes 设置公开路由
func (s *ApiServer) setupPublicRoutes(router *gin.Engine) {
	// 公共中间件
	router.Use(middleware.CORS())

	// 健康检查路由（根路径）
	router.GET("/health", s.healthHandler.HealthCheck)
	router.GET("/health/detailed", s.healthHandler.DetailedHealthCheck)
	router.GET("/health/ready", s.healthHandler.ReadinessCheck)
	router.GET("/health/live", s.healthHandler.LivenessCheck)

	// api v1 路径
	v1 := router.Group("/api/v1")

	// tag相关（公开访问）
	v1.GET("/tag", s.tagHandler.GetTagList)

	// category相关（公开访问）
	v1.GET("/category", s.categoryHandler.GetCategoryList)

	// brand相关（公开访问）
	v1.GET("/brand", s.brandHandler.GetBrandList)
	v1.GET("/brand/:id", s.brandHandler.GetBrandById)
	v1.GET("/brand/slug/:slug", s.brandHandler.GetBrandBySlug)

	// deal相关（公开访问）
	v1.GET("/deal", s.dealHandler.GetDealList)
	v1.GET("/deal/:id", s.dealHandler.GetDealById)
	v1.GET("/deal/slug/:slug", s.dealHandler.GetDealBySlug)

	// coupon相关（公开访问）
	v1.GET("/coupon", s.couponHandler.GetCouponList)
	v1.GET("/coupon/:id", s.couponHandler.GetCouponById)
	v1.GET("/coupon/slug/:slug", s.couponHandler.GetCouponBySlug)
	v1.GET("/coupon/code/:code", s.couponHandler.GetCouponByCode)

	// article相关（公开访问）
	v1.GET("/article", s.articleHandler.GetArticleList)
	v1.GET("/article/:id", s.articleHandler.GetArticleById)
	v1.GET("/article/slug/:slug", s.articleHandler.GetArticleBySlug)
}

// Setup 设置路由
func (s *ApiServer) Setup(router *gin.Engine) {
	// 设置各类路由
	s.setupPublicRoutes(router)
}
