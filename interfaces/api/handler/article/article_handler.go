package article

import (
	"net/http"
	"strconv"

	"brandreviews/application/article/appservice"
	"brandreviews/application/article/dto"
	"brandreviews/infra/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ArticleHandler struct {
	articleAppService appservice.ArticleAppService
	logger            *zap.Logger
}

func NewArticleHandler(
	articleAppService appservice.ArticleAppService,
	logger *zap.Logger,
) *ArticleHandler {
	return &ArticleHandler{
		articleAppService: articleAppService,
		logger:            logger,
	}
}

// GetArticleList 获取文章列表
func (h *ArticleHandler) GetArticleList(c *gin.Context) {
	var req dto.GetArticleListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("Failed to bind query parameters", zap.Error(err))
		response.Error(c, http.StatusBadRequest, "Invalid query parameters")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	resp, appErr := h.articleAppService.GetArticleListByCondition(c, &req)
	if appErr != nil {
		h.logger.Error("Failed to get article list",
			zap.Any("request", req),
			zap.Error(appErr),
		)
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetArticleById 根据ID获取文章详情
func (h *ArticleHandler) GetArticleById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		h.logger.Error("Invalid article ID", zap.String("id", idStr), zap.Error(err))
		response.Error(c, http.StatusBadRequest, "Invalid article ID")
		return
	}

	resp, appErr := h.articleAppService.GetArticleDetailById(c, id)
	if appErr != nil {
		h.logger.Error("Failed to get article by ID",
			zap.Uint64("id", id),
			zap.Error(appErr),
		)
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetArticleBySlug 根据Slug获取文章详情
func (h *ArticleHandler) GetArticleBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Empty article slug")
		response.Error(c, http.StatusBadRequest, "Article slug is required")
		return
	}

	resp, appErr := h.articleAppService.GetArticleDetailBySlug(c, slug)
	if appErr != nil {
		h.logger.Error("Failed to get article by slug",
			zap.String("slug", slug),
			zap.Error(appErr),
		)
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}
