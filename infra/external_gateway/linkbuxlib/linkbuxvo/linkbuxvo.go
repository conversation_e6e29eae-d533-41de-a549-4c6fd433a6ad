package linkbuxvo

type GetCouponListResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalItems int `json:"total_items"`
		TotalPage  int `json:"total_page"`
		Limit      int `json:"limit"`
		Page       int `json:"page"`
		Data       []struct {
			CouponId      string      `json:"coupon_id"`
			Mid           string      `json:"mid"`
			Mcid          string      `json:"mcid"`
			MerchantName  string      `json:"merchant_name"`
			PrimaryRegion interface{} `json:"primary_region"`
			OfferType     interface{} `json:"offer_type"`
			CouponName    string      `json:"coupon_name"`
			CouponCode    string      `json:"coupon_code"`
			Discount      string      `json:"discount"`
			Description   string      `json:"description"`
			BeginDate     string      `json:"begin_date"`
			EndDate       string      `json:"end_date"`
			OriginUrl     string      `json:"origin_url"`
			TrackingUrl   string      `json:"tracking_url"`
			Logo          string      `json:"logo"`
			Category      string      `json:"category"`
		} `json:"data"`
	} `json:"data"`
}
