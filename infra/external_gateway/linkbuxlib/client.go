package linkbuxlib

import (
	"brandreviews/infra/ecode"
	"brandreviews/infra/external_gateway/linkbuxlib/linkbuxvo"
	"brandreviews/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
)

func GetCoupons(token string, limit int, page int) (*linkbuxvo.GetCouponListResp, *ecode.Error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"token": token,
		"mod":   "coupon",
		"op":    "coupons",
		"limit": strconv.Itoa(limit),
		"page":  strconv.Itoa(page),
	}

	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	// Add small delay to prevent rate limiting
	resp, err := remoteInvokeWithUrl(ctx, host+apiGetCoupons, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	couponList := new(linkbuxvo.GetCouponListResp)
	if err := json.Unmarshal(resp, couponList); err != nil {
		return nil, ecode.Wrap(err, ecode.ErrInternalServer.Code, ecode.ErrInternalServer.Message)
	}

	return couponList, nil
}

func BatchGetCoupons(token string, limit int) ([]map[string]interface{}, *ecode.Error) {
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	page := 1

	for {
		resp, err := GetCoupons(token, limit, page)
		if err != nil {
			fmt.Println("GetCoupons:", err)
			continue
		}

		for _, row := range resp.Data.Data {
			if len(row.CouponCode) <= 0 {
				continue
			}

			uniqueKey := row.CouponId
			if _, exists := uniqueMap[uniqueKey]; !exists {
				rowData := map[string]interface{}{}
				rowData["coupon_title"] = row.CouponName
				rowData["coupon_code"] = row.CouponCode
				rowData["discount"] = row.Discount
				rowData["platform_type"] = "linkbux"
				rowData["description"] = row.Description
				rowData["started_at"] = row.BeginDate
				rowData["ended_at"] = row.EndDate
				rowData["original_domain"] = row.OriginUrl
				rowData["domain"] = domainutil.ExtractDomain(row.OriginUrl)
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}
		fmt.Println(page, resp.Data.TotalPage)
		// Check if we've reached the last page
		if page >= resp.Data.TotalPage {
			break
		}
		page++
	}
	return createDataRows, nil
}
