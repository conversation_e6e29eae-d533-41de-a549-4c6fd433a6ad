#!/bin/bash

# 数据库初始化脚本
# 创建数据库、运行迁移、插入测试数据

# 配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-brandreviews}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-password}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️ Brand Reviews 数据库初始化${NC}"
echo "=================================="
echo "数据库主机: $DB_HOST:$DB_PORT"
echo "数据库名称: $DB_NAME"
echo "用户名: $DB_USER"
echo ""

# 检查PostgreSQL是否运行
echo -e "${YELLOW}🔍 检查PostgreSQL服务...${NC}"
if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" > /dev/null 2>&1; then
    echo -e "${RED}❌ PostgreSQL服务未运行或连接失败${NC}"
    echo "请确保PostgreSQL服务正在运行并且连接参数正确"
    exit 1
fi
echo -e "${GREEN}✅ PostgreSQL服务正常${NC}"
echo ""

# 创建数据库（如果不存在）
echo -e "${YELLOW}📦 创建数据库...${NC}"
PGPASSWORD="$DB_PASSWORD" createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 数据库创建成功${NC}"
else
    echo -e "${YELLOW}⚠️ 数据库可能已存在${NC}"
fi
echo ""

# 运行迁移脚本
echo -e "${YELLOW}🔧 运行数据库迁移...${NC}"
if [ -f "migrations/001_optimize_entities.sql" ]; then
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "migrations/001_optimize_entities.sql"
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 迁移执行成功${NC}"
    else
        echo -e "${RED}❌ 迁移执行失败${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️ 迁移文件不存在，跳过迁移${NC}"
fi
echo ""

# 使用GORM自动迁移（推荐方式）
echo -e "${YELLOW}🔄 运行GORM自动迁移...${NC}"
if [ -f "cmd/migrate/main.go" ]; then
    go run cmd/migrate/main.go
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ GORM迁移成功${NC}"
    else
        echo -e "${RED}❌ GORM迁移失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 创建GORM迁移工具...${NC}"
    # 创建迁移工具
    mkdir -p cmd/migrate
    cat > cmd/migrate/main.go << 'EOF'
package main

import (
	"log"

	"brandreviews/config"
	"brandreviews/domain/article/entity"
	brandEntity "brandreviews/domain/brand/entity"
	categoryEntity "brandreviews/domain/category/entity"
	couponEntity "brandreviews/domain/coupon/entity"
	dealEntity "brandreviews/domain/deal/entity"
	tagEntity "brandreviews/domain/tag/entity"
	"brandreviews/infra/database"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewPostgresDB(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 自动迁移
	err = db.AutoMigrate(
		&categoryEntity.Category{},
		&tagEntity.Tag{},
		&brandEntity.Brand{},
		&dealEntity.Deal{},
		&couponEntity.Coupon{},
		&entity.Article{},
	)
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	log.Println("Database migration completed successfully!")
}
EOF
    
    go run cmd/migrate/main.go
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ GORM迁移成功${NC}"
    else
        echo -e "${RED}❌ GORM迁移失败${NC}"
    fi
fi
echo ""

# 插入测试数据
echo -e "${YELLOW}📊 插入测试数据...${NC}"
if [ -f "scripts/seed_data.sql" ]; then
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "scripts/seed_data.sql"
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 测试数据插入成功${NC}"
    else
        echo -e "${RED}❌ 测试数据插入失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 测试数据文件不存在: scripts/seed_data.sql${NC}"
    exit 1
fi
echo ""

# 验证数据
echo -e "${YELLOW}🔍 验证数据插入...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT 
    'categories' as table_name, COUNT(*) as count FROM categories
UNION ALL
SELECT 'tags', COUNT(*) FROM tags
UNION ALL
SELECT 'brands', COUNT(*) FROM brands
UNION ALL
SELECT 'deals', COUNT(*) FROM deals
UNION ALL
SELECT 'coupons', COUNT(*) FROM coupons
UNION ALL
SELECT 'articles', COUNT(*) FROM articles
ORDER BY table_name;
"
echo ""

# 显示一些示例数据
echo -e "${YELLOW}📋 示例数据预览...${NC}"
echo ""
echo -e "${BLUE}品牌数据:${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT id, name, slug, featured, active, category_slug FROM brands LIMIT 5;
"
echo ""

echo -e "${BLUE}优惠活动数据:${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT id, title, discount_type, discount_value, brand_slug, featured FROM deals LIMIT 5;
"
echo ""

echo -e "${BLUE}优惠券数据:${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT id, title, code, discount_type, discount_value, brand_slug FROM coupons LIMIT 5;
"
echo ""

# 创建数据库索引统计
echo -e "${YELLOW}📈 数据库索引信息...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND tablename IN ('brands', 'deals', 'coupons', 'articles', 'categories', 'tags')
ORDER BY tablename, indexname;
"
echo ""

echo -e "${GREEN}🎉 数据库初始化完成！${NC}"
echo "=================================="
echo "✅ 数据库创建完成"
echo "✅ 表结构迁移完成"
echo "✅ 测试数据插入完成"
echo "✅ 索引创建完成"
echo ""
echo -e "${BLUE}📝 下一步：${NC}"
echo "1. 启动API服务: go run cmd/api/main.go"
echo "2. 运行API测试: bash scripts/test_api.sh"
echo "3. 查看API文档: docs/API_DOCUMENTATION.md"
echo ""
echo -e "${YELLOW}💡 数据库连接信息：${NC}"
echo "Host: $DB_HOST:$DB_PORT"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""
echo -e "${YELLOW}🔗 测试URL示例：${NC}"
echo "健康检查: http://localhost:8080/health"
echo "品牌列表: http://localhost:8080/api/v1/brand"
echo "优惠列表: http://localhost:8080/api/v1/deal"
echo "优惠券列表: http://localhost:8080/api/v1/coupon"
