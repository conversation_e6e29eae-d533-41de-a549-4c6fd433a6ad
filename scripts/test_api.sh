#!/bin/bash

# API测试脚本
# 用于测试Brand Reviews API的各个功能

BASE_URL="http://localhost:8080/api/v1"
HEALTH_URL="http://localhost:8080"

echo "🚀 Brand Reviews API 测试脚本"
echo "=================================="
echo "Base URL: $BASE_URL"
echo "Health URL: $HEALTH_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_code="${3:-200}"
    
    echo -e "${BLUE}测试: $name${NC}"
    echo "URL: $url"
    
    response=$(curl -s -w "\n%{http_code}" "$url")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq "$expected_code" ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $http_code)${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ 失败 (HTTP $http_code, 期望 $expected_code)${NC}"
        echo "$body"
    fi
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 检查服务是否运行
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
if ! curl -s "$HEALTH_URL/health" > /dev/null; then
    echo -e "${RED}❌ 服务未运行，请先启动API服务${NC}"
    echo "启动命令: go run cmd/api/main.go"
    exit 1
fi
echo -e "${GREEN}✅ 服务正在运行${NC}"
echo ""

# ============================================================================
# 健康检查测试
# ============================================================================
echo -e "${YELLOW}🏥 健康检查测试${NC}"
echo "=================================="

test_endpoint "基础健康检查" "$HEALTH_URL/health"
test_endpoint "详细健康检查" "$HEALTH_URL/health/detailed"
test_endpoint "就绪检查" "$HEALTH_URL/health/ready"
test_endpoint "存活检查" "$HEALTH_URL/health/live"

# ============================================================================
# 分类API测试
# ============================================================================
echo -e "${YELLOW}📂 分类API测试${NC}"
echo "=================================="

test_endpoint "获取分类列表" "$BASE_URL/category"

# ============================================================================
# 标签API测试
# ============================================================================
echo -e "${YELLOW}🏷️ 标签API测试${NC}"
echo "=================================="

test_endpoint "获取标签列表" "$BASE_URL/tag"

# ============================================================================
# 品牌API测试
# ============================================================================
echo -e "${YELLOW}🏢 品牌API测试${NC}"
echo "=================================="

test_endpoint "获取品牌列表" "$BASE_URL/brand"
test_endpoint "获取品牌列表(分页)" "$BASE_URL/brand?page=1&page_size=5"
test_endpoint "搜索品牌" "$BASE_URL/brand?search=Apple"
test_endpoint "筛选特色品牌" "$BASE_URL/brand?featured=true"
test_endpoint "按分类筛选品牌" "$BASE_URL/brand?category_id=1"
test_endpoint "根据ID获取品牌" "$BASE_URL/brand/1"
test_endpoint "根据slug获取品牌" "$BASE_URL/brand/slug/apple"

# ============================================================================
# 优惠活动API测试
# ============================================================================
echo -e "${YELLOW}🎯 优惠活动API测试${NC}"
echo "=================================="

test_endpoint "获取优惠列表" "$BASE_URL/deal"
test_endpoint "获取优惠列表(分页)" "$BASE_URL/deal?page=1&page_size=3"
test_endpoint "搜索优惠" "$BASE_URL/deal?search=iPhone"
test_endpoint "筛选特色优惠" "$BASE_URL/deal?featured=true"
test_endpoint "筛选已验证优惠" "$BASE_URL/deal?verified=true"
test_endpoint "筛选有效优惠" "$BASE_URL/deal?valid_only=true"
test_endpoint "按品牌筛选优惠" "$BASE_URL/deal?brand_id=1"
test_endpoint "根据ID获取优惠" "$BASE_URL/deal/1"
test_endpoint "根据slug获取优惠" "$BASE_URL/deal/slug/iphone-15-discount"

# ============================================================================
# 优惠券API测试
# ============================================================================
echo -e "${YELLOW}🎫 优惠券API测试${NC}"
echo "=================================="

test_endpoint "获取优惠券列表" "$BASE_URL/coupon"
test_endpoint "获取优惠券列表(分页)" "$BASE_URL/coupon?page=1&page_size=3"
test_endpoint "搜索优惠券" "$BASE_URL/coupon?search=Apple"
test_endpoint "筛选特色优惠券" "$BASE_URL/coupon?featured=true"
test_endpoint "筛选已验证优惠券" "$BASE_URL/coupon?verified=true"
test_endpoint "筛选有效优惠券" "$BASE_URL/coupon?valid_only=true"
test_endpoint "筛选可用优惠券" "$BASE_URL/coupon?available_only=true"
test_endpoint "按折扣类型筛选" "$BASE_URL/coupon?discount_type=percentage"
test_endpoint "根据ID获取优惠券" "$BASE_URL/coupon/1"
test_endpoint "根据slug获取优惠券" "$BASE_URL/coupon/slug/apple-new-user"
test_endpoint "根据代码获取优惠券" "$BASE_URL/coupon/code/APPLE500"

# ============================================================================
# 性能测试
# ============================================================================
echo -e "${YELLOW}⚡ 性能测试${NC}"
echo "=================================="

echo -e "${BLUE}测试缓存性能 (品牌详情)${NC}"
echo "第一次请求 (缓存未命中):"
time curl -s "$BASE_URL/brand/1" > /dev/null
echo ""
echo "第二次请求 (缓存命中):"
time curl -s "$BASE_URL/brand/1" > /dev/null
echo ""

echo -e "${BLUE}测试并发请求${NC}"
echo "发送10个并发请求到品牌列表API..."
for i in {1..10}; do
    curl -s "$BASE_URL/brand?page=$i&page_size=5" > /dev/null &
done
wait
echo "并发请求完成"
echo ""

# ============================================================================
# 错误处理测试
# ============================================================================
echo -e "${YELLOW}❌ 错误处理测试${NC}"
echo "=================================="

test_endpoint "不存在的品牌ID" "$BASE_URL/brand/999999" 404
test_endpoint "不存在的品牌slug" "$BASE_URL/brand/slug/nonexistent" 404
test_endpoint "不存在的优惠券代码" "$BASE_URL/coupon/code/INVALID" 404
test_endpoint "无效的分页参数" "$BASE_URL/brand?page=0" 400

# ============================================================================
# 限流测试
# ============================================================================
echo -e "${YELLOW}🚦 限流测试${NC}"
echo "=================================="

echo -e "${BLUE}测试限流 (快速发送多个请求)${NC}"
echo "发送50个快速请求..."
for i in {1..50}; do
    response=$(curl -s -w "%{http_code}" "$BASE_URL/brand" -o /dev/null)
    if [ "$response" -eq 429 ]; then
        echo -e "${YELLOW}⚠️ 触发限流 (请求 #$i): HTTP 429${NC}"
        break
    fi
done
echo ""

# ============================================================================
# 监控和指标测试
# ============================================================================
echo -e "${YELLOW}📊 监控和指标测试${NC}"
echo "=================================="

# 注意：这些端点可能需要在实际实现中添加
echo -e "${BLUE}尝试获取系统指标...${NC}"
curl -s "$HEALTH_URL/metrics" > /dev/null && echo "✅ 指标端点可用" || echo "⚠️ 指标端点未实现"
curl -s "$HEALTH_URL/metrics/prometheus" > /dev/null && echo "✅ Prometheus指标可用" || echo "⚠️ Prometheus指标未实现"
echo ""

# ============================================================================
# 测试总结
# ============================================================================
echo -e "${GREEN}🎉 API测试完成！${NC}"
echo "=================================="
echo "测试涵盖了以下功能："
echo "✅ 健康检查"
echo "✅ 分类和标签API"
echo "✅ 品牌API (列表、详情、搜索、筛选)"
echo "✅ 优惠活动API (列表、详情、搜索、筛选)"
echo "✅ 优惠券API (列表、详情、搜索、筛选、代码查询)"
echo "✅ 缓存性能"
echo "✅ 并发处理"
echo "✅ 错误处理"
echo "✅ 限流保护"
echo ""
echo -e "${YELLOW}💡 提示：${NC}"
echo "1. 确保数据库中有测试数据 (运行 scripts/seed_data.sql)"
echo "2. 观察响应时间差异 (缓存命中 vs 未命中)"
echo "3. 检查错误响应格式是否一致"
echo "4. 验证限流机制是否正常工作"
echo ""
echo -e "${BLUE}📝 查看详细日志：${NC}"
echo "tail -f logs/app.log"
