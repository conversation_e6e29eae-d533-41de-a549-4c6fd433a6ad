-- 模拟数据种子文件
-- 用于测试API功能和性能

-- 清理现有数据（可选）
-- TRUNCATE TABLE articles, coupons, deals, brands, categories, tags RESTART IDENTITY CASCADE;

-- ============================================================================
-- 插入分类数据
-- ============================================================================
INSERT INTO categories (slug, name, description, featured, active, created_at, updated_at) VALUES
('electronics', '电子产品', '各种电子设备和数码产品', true, true, NOW(), NOW()),
('fashion', '时尚服装', '服装、鞋子和配饰', true, true, NOW(), NOW()),
('home-garden', '家居园艺', '家具、装饰和园艺用品', false, true, NOW(), NOW()),
('sports', '运动户外', '运动器材和户外用品', true, true, NOW(), NOW()),
('beauty', '美容护肤', '化妆品和护肤产品', true, true, NOW(), NOW()),
('books', '图书音像', '书籍、音乐和电影', false, true, NOW(), NOW()),
('food', '食品饮料', '食物和饮品', false, true, NOW(), NOW()),
('automotive', '汽车用品', '汽车配件和用品', false, true, NOW(), NOW());

-- ============================================================================
-- 插入标签数据
-- ============================================================================
INSERT INTO tags (slug, name, description, featured, active, created_at, updated_at) VALUES
('discount', '折扣', '打折优惠商品', true, true, NOW(), NOW()),
('new-arrival', '新品上市', '最新推出的产品', true, true, NOW(), NOW()),
('bestseller', '热销商品', '销量最好的产品', true, true, NOW(), NOW()),
('limited-time', '限时优惠', '限时特价商品', true, true, NOW(), NOW()),
('free-shipping', '免费配送', '包邮商品', false, true, NOW(), NOW()),
('eco-friendly', '环保产品', '环保可持续产品', false, true, NOW(), NOW()),
('premium', '高端产品', '高品质商品', false, true, NOW(), NOW()),
('budget', '经济实惠', '性价比高的商品', false, true, NOW(), NOW());

-- ============================================================================
-- 插入品牌数据
-- ============================================================================
INSERT INTO brands (slug, name, description, logo, website, affiliate_link, featured, active, category_id, category_slug, created_at, updated_at) VALUES
-- 电子产品品牌
('apple', 'Apple', '苹果公司，全球领先的科技公司', 'https://example.com/logos/apple.png', 'https://www.apple.com', 'https://affiliate.apple.com/ref123', true, true, 1, 'electronics', NOW(), NOW()),
('samsung', 'Samsung', '三星电子，韩国跨国电子公司', 'https://example.com/logos/samsung.png', 'https://www.samsung.com', 'https://affiliate.samsung.com/ref456', true, true, 1, 'electronics', NOW(), NOW()),
('sony', 'Sony', '索尼公司，日本电子巨头', 'https://example.com/logos/sony.png', 'https://www.sony.com', 'https://affiliate.sony.com/ref789', false, true, 1, 'electronics', NOW(), NOW()),

-- 时尚品牌
('nike', 'Nike', '耐克，全球知名运动品牌', 'https://example.com/logos/nike.png', 'https://www.nike.com', 'https://affiliate.nike.com/ref101', true, true, 2, 'fashion', NOW(), NOW()),
('adidas', 'Adidas', '阿迪达斯，德国运动品牌', 'https://example.com/logos/adidas.png', 'https://www.adidas.com', 'https://affiliate.adidas.com/ref102', true, true, 2, 'fashion', NOW(), NOW()),
('zara', 'Zara', 'Zara，西班牙快时尚品牌', 'https://example.com/logos/zara.png', 'https://www.zara.com', 'https://affiliate.zara.com/ref103', false, true, 2, 'fashion', NOW(), NOW()),

-- 美容品牌
('loreal', "L'Oréal", '欧莱雅，法国化妆品集团', 'https://example.com/logos/loreal.png', 'https://www.loreal.com', 'https://affiliate.loreal.com/ref201', true, true, 5, 'beauty', NOW(), NOW()),
('estee-lauder', 'Estée Lauder', '雅诗兰黛，美国化妆品公司', 'https://example.com/logos/estee-lauder.png', 'https://www.esteelauder.com', 'https://affiliate.esteelauder.com/ref202', false, true, 5, 'beauty', NOW(), NOW());

-- ============================================================================
-- 插入优惠活动数据
-- ============================================================================
INSERT INTO deals (slug, title, description, deal_url, image_url, original_price, sale_price, discount_type, discount_value, featured, active, verified, start_date, end_date, brand_id, brand_slug, category_id, category_slug, created_at, updated_at) VALUES
-- Apple 优惠
('iphone-15-discount', 'iPhone 15 限时优惠', 'iPhone 15 系列限时折扣，立省1000元', 'https://apple.com/iphone-15-deal', 'https://example.com/deals/iphone15.jpg', 7999.00, 6999.00, 'fixed', 1000.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '30 days')::bigint, 1, 'apple', 1, 'electronics', NOW(), NOW()),
('macbook-sale', 'MacBook Air 春季促销', 'MacBook Air M2 芯片版本春季特价', 'https://apple.com/macbook-sale', 'https://example.com/deals/macbook.jpg', 8999.00, 7999.00, 'percentage', 11.11, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '15 days')::bigint, 1, 'apple', 1, 'electronics', NOW(), NOW()),

-- Samsung 优惠
('galaxy-s24-promo', 'Galaxy S24 新品促销', 'Galaxy S24 系列新品上市特惠', 'https://samsung.com/galaxy-s24-promo', 'https://example.com/deals/galaxy-s24.jpg', 6999.00, 5999.00, 'fixed', 1000.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '20 days')::bigint, 2, 'samsung', 1, 'electronics', NOW(), NOW()),

-- Nike 优惠
('nike-spring-sale', 'Nike 春季大促', '全场运动鞋服春季大促销', 'https://nike.com/spring-sale', 'https://example.com/deals/nike-spring.jpg', 899.00, 599.00, 'percentage', 33.37, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '25 days')::bigint, 4, 'nike', 2, 'fashion', NOW(), NOW()),
('air-jordan-discount', 'Air Jordan 限量折扣', 'Air Jordan 经典款式限量折扣', 'https://nike.com/air-jordan-deal', 'https://example.com/deals/air-jordan.jpg', 1299.00, 999.00, 'fixed', 300.00, false, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '10 days')::bigint, 4, 'nike', 2, 'fashion', NOW(), NOW()),

-- L'Oréal 优惠
('loreal-skincare-sale', "L'Oréal 护肤品特惠", '欧莱雅护肤系列产品特价销售', 'https://loreal.com/skincare-sale', 'https://example.com/deals/loreal-skincare.jpg', 299.00, 199.00, 'percentage', 33.44, false, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '18 days')::bigint, 7, 'loreal', 5, 'beauty', NOW(), NOW());

-- ============================================================================
-- 插入优惠券数据
-- ============================================================================
INSERT INTO coupons (slug, title, description, code, coupon_url, discount_type, discount_value, min_order_value, max_discount, featured, active, verified, start_date, end_date, usage_limit, used_count, user_usage_limit, brand_id, brand_slug, category_id, category_slug, created_at, updated_at) VALUES
-- Apple 优惠券
('apple-new-user', 'Apple 新用户专享', '新用户注册即可获得500元优惠券', 'APPLE500', 'https://apple.com/coupon/new-user', 'fixed', 500.00, 3000.00, 500.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '60 days')::bigint, 1000, 156, 1, 1, 'apple', 1, 'electronics', NOW(), NOW()),
('apple-vip', 'Apple VIP 会员券', 'VIP会员专享8折优惠券', 'APPLEVIP20', 'https://apple.com/coupon/vip', 'percentage', 20.00, 5000.00, 2000.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '90 days')::bigint, 500, 89, 1, 1, 'apple', 1, 'electronics', NOW(), NOW()),

-- Samsung 优惠券
('samsung-spring', 'Samsung 春季优惠券', '春季购物节专享优惠券', 'SAMSUNG300', 'https://samsung.com/coupon/spring', 'fixed', 300.00, 2000.00, 300.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '45 days')::bigint, 2000, 445, 1, 2, 'samsung', 1, 'electronics', NOW(), NOW()),

-- Nike 优惠券
('nike-member', 'Nike 会员专享券', 'Nike会员专享15%折扣券', 'NIKEMEMBER15', 'https://nike.com/coupon/member', 'percentage', 15.00, 500.00, 500.00, true, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '30 days')::bigint, 5000, 1234, 2, 4, 'nike', 2, 'fashion', NOW(), NOW()),
('nike-free-shipping', 'Nike 免费配送券', '满299元免费配送', 'NIKEFREE', 'https://nike.com/coupon/free-shipping', 'free_shipping', 0.00, 299.00, 0.00, false, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '120 days')::bigint, 0, 2567, 3, 4, 'nike', 2, 'fashion', NOW(), NOW()),

-- L'Oréal 优惠券
('loreal-beauty', "L'Oréal 美妆优惠券", '美妆产品专享25%折扣', 'BEAUTY25', 'https://loreal.com/coupon/beauty', 'percentage', 25.00, 200.00, 200.00, false, true, true, EXTRACT(EPOCH FROM NOW())::bigint, EXTRACT(EPOCH FROM NOW() + INTERVAL '35 days')::bigint, 3000, 678, 1, 7, 'loreal', 5, 'beauty', NOW(), NOW());

-- ============================================================================
-- 插入文章数据
-- ============================================================================
INSERT INTO articles (slug, title, description, content, featured_image, published_at, view_count, last_viewed_at, meta_title, meta_description, author_name, last_updated_by, featured, published, trending, category_id, category_slug, brand_id, brand_slug, deal_ids, tag_ids, created_at, updated_at) VALUES
-- 电子产品文章
('iphone-15-review', 'iPhone 15 深度评测：值得升级吗？', 'iPhone 15 系列全面评测，从性能到拍照功能详细分析', 
'iPhone 15 作为苹果2023年的旗舰产品，带来了诸多创新功能。本文将从设计、性能、拍照、续航等多个维度进行深度评测...[详细内容]', 
'https://example.com/articles/iphone15-review.jpg', 
EXTRACT(EPOCH FROM NOW() - INTERVAL '5 days')::bigint, 
15420, NOW() - INTERVAL '2 hours', 
'iPhone 15 评测 - 2024年最值得买的手机', 
'iPhone 15 深度评测，性能测试，拍照对比，是否值得升级，详细分析帮你做决定', 
'张科技', '编辑部', true, true, true, 1, 'electronics', 1, 'apple', '{1,2}', '{1,2,3}', NOW() - INTERVAL '5 days', NOW()),

('samsung-vs-apple', 'Samsung Galaxy S24 vs iPhone 15：旗舰对决', 'Android和iOS阵营的旗舰对比', 
'2024年旗舰手机对决，Samsung Galaxy S24与iPhone 15的全方位对比分析...[详细内容]', 
'https://example.com/articles/samsung-vs-apple.jpg', 
EXTRACT(EPOCH FROM NOW() - INTERVAL '3 days')::bigint, 
8930, NOW() - INTERVAL '1 hour', 
'Galaxy S24 vs iPhone 15 对比评测', 
'三星Galaxy S24和苹果iPhone 15详细对比，性能、拍照、系统全面分析', 
'李评测', '编辑部', true, true, false, 1, 'electronics', 2, 'samsung', '{3}', '{1,3,7}', NOW() - INTERVAL '3 days', NOW()),

-- 时尚文章
('nike-spring-trends', '2024春季运动时尚趋势', 'Nike引领的春季运动时尚潮流', 
'2024年春季，运动时尚再次成为潮流焦点。Nike作为行业领导者，推出了多款引领潮流的产品...[详细内容]', 
'https://example.com/articles/nike-spring-trends.jpg', 
EXTRACT(EPOCH FROM NOW() - INTERVAL '7 days')::bigint, 
6750, NOW() - INTERVAL '3 hours', 
'2024春季运动时尚趋势 - Nike新品推荐', 
'Nike 2024春季新品，运动时尚趋势，穿搭指南，最新款式推荐', 
'王时尚', '时尚编辑', false, true, true, 2, 'fashion', 4, 'nike', '{4,5}', '{2,4,7}', NOW() - INTERVAL '7 days', NOW()),

-- 美容文章
('skincare-routine-2024', '2024年护肤新趋势：科学护肤指南', '基于最新研究的护肤方法', 
'2024年护肤科学有了新的突破，本文将介绍最新的护肤理念和产品推荐...[详细内容]', 
'https://example.com/articles/skincare-2024.jpg', 
EXTRACT(EPOCH FROM NOW() - INTERVAL '2 days')::bigint, 
12340, NOW() - INTERVAL '30 minutes', 
'2024护肤新趋势 - 科学护肤完整指南', 
'2024年最新护肤趋势，科学护肤方法，产品推荐，护肤步骤详解', 
'陈美容', '美容编辑', true, true, true, 5, 'beauty', 7, 'loreal', '{6}', '{1,6,7}', NOW() - INTERVAL '2 days', NOW()),

-- 综合购物指南
('best-deals-march-2024', '2024年3月最佳优惠汇总', '本月不容错过的优惠活动', 
'3月份各大品牌推出了众多优惠活动，本文为您汇总最值得关注的优惠信息...[详细内容]', 
'https://example.com/articles/march-deals.jpg', 
EXTRACT(EPOCH FROM NOW() - INTERVAL '1 day')::bigint, 
23450, NOW() - INTERVAL '15 minutes', 
'2024年3月最佳优惠活动汇总', 
'3月优惠活动，品牌折扣，优惠券推荐，购物指南，省钱攻略', 
'刘购物', '编辑部', true, true, true, 1, 'electronics', NULL, NULL, '{1,2,3,4,5,6}', '{1,4,5}', NOW() - INTERVAL '1 day', NOW());

-- ============================================================================
-- 更新统计信息
-- ============================================================================
ANALYZE categories;
ANALYZE tags;
ANALYZE brands;
ANALYZE deals;
ANALYZE coupons;
ANALYZE articles;

-- ============================================================================
-- 验证数据插入
-- ============================================================================
SELECT 'Categories' as table_name, COUNT(*) as count FROM categories
UNION ALL
SELECT 'Tags', COUNT(*) FROM tags
UNION ALL
SELECT 'Brands', COUNT(*) FROM brands
UNION ALL
SELECT 'Deals', COUNT(*) FROM deals
UNION ALL
SELECT 'Coupons', COUNT(*) FROM coupons
UNION ALL
SELECT 'Articles', COUNT(*) FROM articles;
