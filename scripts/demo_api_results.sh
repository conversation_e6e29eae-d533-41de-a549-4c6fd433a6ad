#!/bin/bash

# API演示脚本 - 展示实际调用结果
# 此脚本展示各个API端点的实际响应数据

BASE_URL="http://localhost:9091/api/v1"
HEALTH_URL="http://localhost:9091"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 美化输出函数
print_header() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC} ${CYAN}$1${NC} ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_section() {
    echo ""
    echo -e "${BLUE}▶ $1${NC}"
    echo -e "${BLUE}$2${NC}"
    echo ""
}

demo_api() {
    local title="$1"
    local url="$2"
    local description="$3"
    
    print_section "$title" "$description"
    echo -e "${YELLOW}请求URL:${NC} $url"
    echo ""
    echo -e "${GREEN}响应结果:${NC}"
    echo "----------------------------------------"
    
    response=$(curl -s "$url")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    
    echo "----------------------------------------"
    echo ""
}

# 检查依赖
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️ 建议安装 jq 以获得更好的JSON格式化显示${NC}"
    echo "安装命令: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
    echo ""
fi

print_header "🚀 Brand Reviews API 演示 - 实际调用结果展示"

echo -e "${CYAN}本演示将展示以下功能：${NC}"
echo "✨ 健康检查和系统状态"
echo "📂 分类和标签管理"
echo "🏢 品牌信息查询"
echo "📝 文章内容管理"
echo "🎯 优惠活动展示"
echo "🎫 优惠券管理"
echo "⚡ 性能和缓存效果"
echo ""

# 检查服务状态
echo -e "${YELLOW}🔍 检查API服务状态...${NC}"
if curl -s "$HEALTH_URL/health" > /dev/null; then
    echo -e "${GREEN}✅ API服务正在运行${NC}"
else
    echo -e "${RED}❌ API服务未运行，请先启动服务${NC}"
    echo "启动命令: go run cmd/api/main.go"
    exit 1
fi

# ============================================================================
# 健康检查演示
# ============================================================================
print_header "🏥 健康检查和系统监控"

demo_api "基础健康检查" \
    "$HEALTH_URL/health" \
    "检查系统整体健康状态，包括数据库、缓存等组件"

demo_api "详细健康检查" \
    "$HEALTH_URL/health/detailed" \
    "获取详细的系统指标，包括内存使用、连接池状态等"

# ============================================================================
# 分类和标签演示
# ============================================================================
print_header "📂 分类和标签管理"

demo_api "获取所有分类" \
    "$BASE_URL/category" \
    "获取系统中所有商品分类信息"

demo_api "获取所有标签" \
    "$BASE_URL/tag" \
    "获取系统中所有标签信息，用于商品标记"

# ============================================================================
# 品牌管理演示
# ============================================================================
print_header "🏢 品牌信息管理"

demo_api "获取品牌列表" \
    "$BASE_URL/brand" \
    "获取所有品牌信息，支持分页和筛选"

demo_api "分页获取品牌" \
    "$BASE_URL/brand?page=1&page_size=3" \
    "演示分页功能，每页显示3个品牌"

demo_api "搜索品牌" \
    "$BASE_URL/brand?search=Apple" \
    "根据品牌名称搜索，支持模糊匹配"

demo_api "筛选特色品牌" \
    "$BASE_URL/brand?featured=true" \
    "只显示标记为特色的品牌"

demo_api "按分类筛选品牌" \
    "$BASE_URL/brand?category_id=1" \
    "获取指定分类下的所有品牌"

demo_api "根据ID获取品牌详情" \
    "$BASE_URL/brand/1" \
    "获取指定ID的品牌详细信息"

demo_api "根据Slug获取品牌详情" \
    "$BASE_URL/brand/slug/apple" \
    "通过SEO友好的slug获取品牌信息"

# ============================================================================
# 文章内容演示
# ============================================================================
print_header "📝 文章内容管理"

demo_api "获取文章列表" \
    "$BASE_URL/article" \
    "获取所有文章内容，包括标题、描述和发布信息"

demo_api "分页获取文章" \
    "$BASE_URL/article?page=1&page_size=3" \
    "演示文章分页功能，每页显示3篇文章"

demo_api "搜索文章" \
    "$BASE_URL/article?search=iPhone" \
    "根据文章标题和内容搜索，支持模糊匹配"

demo_api "按品牌筛选文章" \
    "$BASE_URL/article?brand=apple" \
    "获取指定品牌相关的所有文章"

demo_api "按分类筛选文章" \
    "$BASE_URL/article?category=electronics" \
    "获取指定分类下的所有文章"

demo_api "根据ID获取文章详情" \
    "$BASE_URL/article/1" \
    "获取指定ID的文章详细内容"

demo_api "根据Slug获取文章详情" \
    "$BASE_URL/article/slug/iphone-15-review" \
    "通过SEO友好的slug获取文章详细信息"

# ============================================================================
# 优惠活动演示
# ============================================================================
print_header "🎯 优惠活动管理"

demo_api "获取优惠活动列表" \
    "$BASE_URL/deal" \
    "获取所有优惠活动，包括折扣信息和时间限制"

demo_api "搜索优惠活动" \
    "$BASE_URL/deal?search=iPhone" \
    "根据商品名称搜索相关优惠活动"

demo_api "筛选特色优惠" \
    "$BASE_URL/deal?featured=true" \
    "只显示推荐的特色优惠活动"

demo_api "筛选已验证优惠" \
    "$BASE_URL/deal?verified=true" \
    "只显示经过验证的真实优惠"

demo_api "筛选当前有效优惠" \
    "$BASE_URL/deal?valid_only=true" \
    "只显示当前时间有效的优惠活动"

demo_api "按品牌筛选优惠" \
    "$BASE_URL/deal?brand_id=1" \
    "获取指定品牌的所有优惠活动"

demo_api "获取优惠详情" \
    "$BASE_URL/deal/1" \
    "获取指定优惠活动的详细信息"

# ============================================================================
# 优惠券演示
# ============================================================================
print_header "🎫 优惠券管理"

demo_api "获取优惠券列表" \
    "$BASE_URL/coupon" \
    "获取所有优惠券，包括使用条件和限制"

demo_api "搜索优惠券" \
    "$BASE_URL/coupon?search=Apple" \
    "根据品牌或描述搜索优惠券"

demo_api "筛选可用优惠券" \
    "$BASE_URL/coupon?available_only=true" \
    "只显示未达到使用限制的可用优惠券"

demo_api "按折扣类型筛选" \
    "$BASE_URL/coupon?discount_type=percentage" \
    "筛选百分比折扣类型的优惠券"

demo_api "根据优惠码查询" \
    "$BASE_URL/coupon/code/APPLE500" \
    "通过优惠码直接查询优惠券信息"

demo_api "获取优惠券详情" \
    "$BASE_URL/coupon/1" \
    "获取指定优惠券的详细信息"

# ============================================================================
# 性能演示
# ============================================================================
print_header "⚡ 性能和缓存效果演示"

print_section "缓存性能测试" "演示缓存对响应时间的影响"

echo -e "${YELLOW}第一次请求 (缓存未命中):${NC}"
time_start=$(date +%s%N)
curl -s "$BASE_URL/brand/1" > /dev/null
time_end=$(date +%s%N)
time_diff=$(( (time_end - time_start) / 1000000 ))
echo "响应时间: ${time_diff}ms"
echo ""

echo -e "${YELLOW}第二次请求 (缓存命中):${NC}"
time_start=$(date +%s%N)
curl -s "$BASE_URL/brand/1" > /dev/null
time_end=$(date +%s%N)
time_diff=$(( (time_end - time_start) / 1000000 ))
echo "响应时间: ${time_diff}ms"
echo ""

print_section "并发处理测试" "测试API的并发处理能力"
echo "发送10个并发请求..."
start_time=$(date +%s)
for i in {1..10}; do
    curl -s "$BASE_URL/brand?page=$i&page_size=2" > /dev/null &
done
wait
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "10个并发请求完成时间: ${duration}秒"
echo ""

# ============================================================================
# 错误处理演示
# ============================================================================
print_header "❌ 错误处理演示"

demo_api "不存在的资源 (404错误)" \
    "$BASE_URL/brand/999999" \
    "演示访问不存在资源时的错误响应"

demo_api "无效参数 (400错误)" \
    "$BASE_URL/brand?page=0" \
    "演示传入无效参数时的错误响应"

demo_api "不存在的优惠码" \
    "$BASE_URL/coupon/code/INVALID123" \
    "演示查询不存在优惠码时的错误响应"

# ============================================================================
# 高级功能演示
# ============================================================================
print_header "🔧 高级功能演示"

demo_api "复合筛选查询" \
    "$BASE_URL/deal?featured=true&verified=true&valid_only=true&page_size=5" \
    "演示多条件组合筛选功能"

demo_api "品牌关联查询" \
    "$BASE_URL/deal?brand_id=1&category_id=1" \
    "演示品牌和分类的关联查询"

# ============================================================================
# 总结
# ============================================================================
print_header "🎉 演示完成"

echo -e "${GREEN}✅ 演示涵盖的功能：${NC}"
echo "🏥 健康检查和系统监控"
echo "📂 分类和标签管理"
echo "🏢 品牌信息查询和筛选"
echo "📝 文章内容管理和查询"
echo "🎯 优惠活动管理"
echo "🎫 优惠券管理和查询"
echo "⚡ 缓存性能优化"
echo "🔧 错误处理机制"
echo "🚀 高级查询功能"
echo ""

echo -e "${BLUE}📊 性能特点：${NC}"
echo "• 缓存命中时响应时间 < 10ms"
echo "• 支持高并发请求处理"
echo "• 智能错误处理和响应"
echo "• RESTful API设计"
echo "• 完整的数据验证"
echo ""

echo -e "${YELLOW}💡 使用建议：${NC}"
echo "1. 利用缓存机制提高性能"
echo "2. 使用分页避免大量数据传输"
echo "3. 合理使用筛选条件精确查询"
echo "4. 通过slug进行SEO友好的访问"
echo "5. 监控健康检查端点确保服务稳定"
echo ""

echo -e "${CYAN}🔗 相关文档：${NC}"
echo "• API文档: docs/API_DOCUMENTATION.md"
echo "• 数据库迁移: migrations/001_optimize_entities.sql"
echo "• 配置示例: config.example.yaml"
