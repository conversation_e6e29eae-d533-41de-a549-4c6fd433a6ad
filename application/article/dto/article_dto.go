package dto

import (
	"brandreviews/domain/article/entity"
	"time"

	"github.com/lib/pq"
)

// GetArticleListReq 获取博客列表请求
type GetArticleListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Category string `form:"category"`
	Search   string `form:"search"`
	Brand    string `form:"brand"`
	Tag      string `form:"tag"`
}

// ArticleDetailResp 博客信息
type ArticleDetailResp struct {
	Id   uint64 `json:"id"`
	Slug string `json:"slug"`

	Title         string        `json:"title"`
	Description   string        `json:"description"`
	Content       string        `json:"content"`
	FeaturedImage string        `json:"featured_image"`
	PublishedAt   time.Time     `json:"published_at"`
	CategoryInfo  string        `json:"category_info"`
	DealList      pq.Int64Array `json:"deal_list"`
	TagList       pq.Int64Array `json:"tag_list"`
	BrandInfo     uint64        `json:"brand_info"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ArticleListResp 获取博客列表响应
type ArticleListResp struct {
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	ArticleList []*ArticleDetailResp `json:"article_list"`
}

func (req *GetArticleListReq) Dto2ConditionGetArticleList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Category != "" {
		condition["category"] = req.Category
	}
	if req.Brand != "" {
		condition["brand"] = req.Brand
	}
	return condition
}

// Entity2DtoArticleDetailResp converts article entity to detail response DTO
func Entity2DtoArticleDetailResp(article *entity.Article) *ArticleDetailResp {
	if article == nil {
		return nil
	}

	resp := &ArticleDetailResp{
		Id:            article.Id,
		Slug:          article.Slug,
		Title:         article.Title,
		Description:   article.Description,
		Content:       article.Content,
		FeaturedImage: article.FeaturedImage,
		CreatedAt:     article.CreatedAt,
		UpdatedAt:     article.UpdatedAt,
	}

	// Convert published timestamp to time
	if article.PublishedAt > 0 {
		resp.PublishedAt = time.Unix(int64(article.PublishedAt), 0)
	}

	// Set category info (could be enhanced to include category name)
	resp.CategoryInfo = string(rune(article.CategoryId))

	// Set brand info
	resp.BrandInfo = article.BrandId

	// Convert deal and tag arrays
	resp.DealList = article.DealIds
	resp.TagList = article.TagIds

	return resp
}

// Entity2DtoArticleListResp converts article entities to list response DTO
func Entity2DtoArticleListResp(page int, pageSize int, total int64, articles []*entity.Article) *ArticleListResp {
	resp := &ArticleListResp{
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		ArticleList: make([]*ArticleDetailResp, 0, len(articles)),
	}

	for _, article := range articles {
		if articleResp := Entity2DtoArticleDetailResp(article); articleResp != nil {
			resp.ArticleList = append(resp.ArticleList, articleResp)
		}
	}

	return resp
}
