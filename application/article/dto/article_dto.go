package dto

import (
	"time"

	"brandreviews/domain/article/entity"
)

// GetArticleListReq 获取博客列表请求
type GetArticleListReq struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Category string `form:"category"`
	Search   string `form:"search"`
	Brand    string `form:"brand"`
	Tag      string `form:"tag"`
}

// ArticleDetailResp 博客信息
type ArticleDetailResp struct {
	Id   uint64 `json:"id"`
	Slug string `json:"slug"`

	Title         string    `json:"title"`
	Description   string    `json:"description"`
	Content       string    `json:"content"`
	FeaturedImage string    `json:"featured_image"`
	PublishedAt   time.Time `json:"published_at"`

	// 关联对象的完整信息
	CategoryInfo interface{} `json:"category_info"` // Will be *categorydto.CategoryDetailResp
	BrandInfo    interface{} `json:"brand_info"`    // Will be *branddto.BrandDetailResp
	DealList     interface{} `json:"deal_list"`     // Will be []*dealdto.DealDetailResp
	TagList      interface{} `json:"tag_list"`      // Will be []*tagdto.TagDetailResp
	CouponList   interface{} `json:"coupon_list"`   // Will be []*coupondto.CouponDetailResp

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ArticleListResp 获取博客列表响应
type ArticleListResp struct {
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	ArticleList []*ArticleDetailResp `json:"article_list"`
}

func (req *GetArticleListReq) Dto2ConditionGetArticleList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Category != "" {
		condition["category"] = req.Category
	}
	if req.Brand != "" {
		condition["brand"] = req.Brand
	}
	return condition
}

// Entity2DtoArticleDetailResp converts article entity to detail response DTO with related data
func Entity2DtoArticleDetailResp(
	article *entity.Article,
	brandInfo interface{},
	categoryInfo interface{},
	dealList interface{},
	tagList interface{},
	couponList interface{},
) *ArticleDetailResp {
	if article == nil {
		return nil
	}

	resp := &ArticleDetailResp{
		Id:            article.Id,
		Slug:          article.Slug,
		Title:         article.Title,
		Description:   article.Description,
		Content:       article.Content,
		FeaturedImage: article.FeaturedImage,
		CreatedAt:     article.CreatedAt,
		UpdatedAt:     article.UpdatedAt,
		BrandInfo:     brandInfo,
		CategoryInfo:  categoryInfo,
		DealList:      dealList,
		TagList:       tagList,
		CouponList:    couponList,
	}

	// Convert published timestamp to time
	if article.PublishedAt > 0 {
		resp.PublishedAt = time.Unix(int64(article.PublishedAt), 0)
	}

	return resp
}

// Entity2DtoArticleListResp converts article entities to list response DTO with related data
func Entity2DtoArticleListResp(
	page int,
	pageSize int,
	total int64,
	articles []*entity.Article,
	relatedData map[uint64]map[string]interface{}, // map[articleId]map[dataType]data
) *ArticleListResp {
	resp := &ArticleListResp{
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		ArticleList: make([]*ArticleDetailResp, 0, len(articles)),
	}

	for _, article := range articles {
		var brandInfo, categoryInfo, dealList, tagList, couponList interface{}

		if data, exists := relatedData[article.Id]; exists {
			brandInfo = data["brand"]
			categoryInfo = data["category"]
			dealList = data["deals"]
			tagList = data["tags"]
			couponList = data["coupons"]
		}

		if articleResp := Entity2DtoArticleDetailResp(article, brandInfo, categoryInfo, dealList, tagList, couponList); articleResp != nil {
			resp.ArticleList = append(resp.ArticleList, articleResp)
		}
	}

	return resp
}
