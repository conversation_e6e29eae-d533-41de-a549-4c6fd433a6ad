package appservice

import (
	"brandreviews/application/article/dto"
	brandapp "brandreviews/application/brand/appservice"
	categoryapp "brandreviews/application/category/appservice"
	couponapp "brandreviews/application/coupon/appservice"
	dealapp "brandreviews/application/deal/appservice"
	tagapp "brandreviews/application/tag/appservice"
	"brandreviews/domain/article/entity"
	"brandreviews/domain/article/service"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type ArticleAppService interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
}

type ArticleAppServiceImpl struct {
	articleService  service.ArticleService
	brandService    brandapp.BrandAppService
	categoryService categoryapp.CategoryAppService
	dealService     dealapp.DealAppService
	tagService      tagapp.TagAppService
	couponService   couponapp.CouponAppService
}

func NewArticleAppService(
	articleService service.ArticleService,
	brandService brandapp.BrandAppService,
	categoryService categoryapp.CategoryAppService,
	dealService dealapp.DealAppService,
	tagService tagapp.TagAppService,
	couponService couponapp.CouponAppService,
) ArticleAppService {
	return &ArticleAppServiceImpl{
		articleService:  articleService,
		brandService:    brandService,
		categoryService: categoryService,
		dealService:     dealService,
		tagService:      tagService,
		couponService:   couponService,
	}
}

func (app *ArticleAppServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.articleService.GetArticleCount(ctx)
}

func (app *ArticleAppServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// Fetch related data
	brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

	return dto.Entity2DtoArticleDetailResp(article, brandInfo, categoryInfo, dealList, tagList, couponList), nil
}

func (app *ArticleAppServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}

	// Fetch related data
	brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

	return dto.Entity2DtoArticleDetailResp(article, brandInfo, categoryInfo, dealList, tagList, couponList), nil
}

func (app *ArticleAppServiceImpl) GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetArticleList()
	articles, total, err := app.articleService.GetArticleListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// Fetch related data for all articles
	relatedData := app.fetchRelatedDataForList(ctx, articles)

	return dto.Entity2DtoArticleListResp(req.Page, req.PageSize, total, articles, relatedData), nil
}

func (app *ArticleAppServiceImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.CreateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.UpdateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	return app.articleService.DeleteArticle(ctx, id)
}

// fetchRelatedData fetches all related data for a single article
func (app *ArticleAppServiceImpl) fetchRelatedData(ctx *gin.Context, article *entity.Article) (
	brandInfo, categoryInfo, dealList, tagList, couponList interface{},
) {
	// Initialize empty slices
	dealList = []interface{}{}
	tagList = []interface{}{}
	couponList = []interface{}{}

	// Fetch brand information
	if article.BrandId > 0 {
		if brand, err := app.brandService.GetBrandDetailById(ctx, article.BrandId); err == nil {
			brandInfo = brand
		}
		// If brand not found, brandInfo will remain nil
	}

	// Fetch category information
	if article.CategoryId > 0 {
		if category, err := app.categoryService.GetCategoryDetailById(ctx, article.CategoryId); err == nil {
			categoryInfo = category
		}
		// If category not found, categoryInfo will remain nil
	}

	// Fetch deal information
	if len(article.DealIds) > 0 {
		deals := []interface{}{}
		for _, dealId := range article.DealIds {
			if deal, err := app.dealService.GetDealDetailById(ctx, uint64(dealId)); err == nil {
				deals = append(deals, deal)
			}
			// If deal not found, skip it
		}
		dealList = deals
	}

	// Fetch tag information
	if len(article.TagIds) > 0 {
		tags := []interface{}{}
		for _, tagId := range article.TagIds {
			if tag, err := app.tagService.GetTagDetailById(ctx, uint64(tagId)); err == nil {
				tags = append(tags, tag)
			}
			// If tag not found, skip it
		}
		tagList = tags
	}

	// Fetch coupon information (if any coupons are related to this article)
	// For now, articles don't have direct coupon relationships
	// This could be extended in the future if needed
	couponList = []interface{}{}

	return brandInfo, categoryInfo, dealList, tagList, couponList
}

// fetchRelatedDataForList fetches related data for multiple articles
func (app *ArticleAppServiceImpl) fetchRelatedDataForList(ctx *gin.Context, articles []*entity.Article) map[uint64]map[string]interface{} {
	relatedData := make(map[uint64]map[string]interface{})

	for _, article := range articles {
		brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

		relatedData[article.Id] = map[string]interface{}{
			"brand":    brandInfo,
			"category": categoryInfo,
			"deals":    dealList,
			"tags":     tagList,
			"coupons":  couponList,
		}
	}

	return relatedData
}
