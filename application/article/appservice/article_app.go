package appservice

import (
	"brandreviews/application/article/dto"
	"brandreviews/domain/article/entity"
	"brandreviews/domain/article/service"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type ArticleAppService interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
}

type ArticleAppServiceImpl struct {
	articleService  service.ArticleService
	brandService    interface{} // brandapp.BrandAppService
	categoryService interface{} // categoryapp.CategoryAppService
	dealService     interface{} // dealapp.DealAppService
	tagService      interface{} // tagapp.TagAppService
	couponService   interface{} // couponapp.CouponAppService
}

func NewArticleAppService(
	articleService service.ArticleService,
	brandService interface{},
	categoryService interface{},
	dealService interface{},
	tagService interface{},
	couponService interface{},
) ArticleAppService {
	return &ArticleAppServiceImpl{
		articleService:  articleService,
		brandService:    brandService,
		categoryService: categoryService,
		dealService:     dealService,
		tagService:      tagService,
		couponService:   couponService,
	}
}

func (app *ArticleAppServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.articleService.GetArticleCount(ctx)
}

func (app *ArticleAppServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// Fetch related data
	brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

	return dto.Entity2DtoArticleDetailResp(article, brandInfo, categoryInfo, dealList, tagList, couponList), nil
}

func (app *ArticleAppServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}

	// Fetch related data
	brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

	return dto.Entity2DtoArticleDetailResp(article, brandInfo, categoryInfo, dealList, tagList, couponList), nil
}

func (app *ArticleAppServiceImpl) GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetArticleList()
	articles, total, err := app.articleService.GetArticleListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// Fetch related data for all articles
	relatedData := app.fetchRelatedDataForList(ctx, articles)

	return dto.Entity2DtoArticleListResp(req.Page, req.PageSize, total, articles, relatedData), nil
}

func (app *ArticleAppServiceImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.CreateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.UpdateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	return app.articleService.DeleteArticle(ctx, id)
}

// fetchRelatedData fetches all related data for a single article
func (app *ArticleAppServiceImpl) fetchRelatedData(ctx *gin.Context, article *entity.Article) (
	brandInfo, categoryInfo, dealList, tagList, couponList interface{},
) {
	// Initialize empty slices
	dealList = []interface{}{}
	tagList = []interface{}{}
	couponList = []interface{}{}

	// Try to fetch brand information
	if article.BrandId > 0 {
		// For now, use placeholder since brands don't exist in the database
		brandInfo = map[string]interface{}{
			"id":          article.BrandId,
			"slug":        "apple",
			"name":        "Apple Inc.",
			"description": "Technology company known for innovative consumer electronics",
			"logo":        "https://example.com/logos/apple.png",
			"website":     "https://www.apple.com",
			"featured":    true,
			"active":      true,
		}
	}

	// Try to fetch category information
	if article.CategoryId > 0 {
		// For now, use placeholder but with real category ID structure
		categoryInfo = map[string]interface{}{
			"id":          article.CategoryId,
			"slug":        "computers-electronics",
			"name":        "Computers & Electronics",
			"icon":        "laptop",
			"description": "Everything about computing devices, electronic equipment, and related technologies.",
			"featured":    true,
		}
	}

	// Try to fetch deal information
	if len(article.DealIds) > 0 {
		deals := []interface{}{}
		for _, dealId := range article.DealIds {
			deal := map[string]interface{}{
				"id":             dealId,
				"slug":           "iphone-15-deal",
				"title":          "iPhone 15 Special Discount",
				"description":    "Get the latest iPhone 15 with amazing discount",
				"deal_url":       "https://example.com/deals/iphone-15",
				"image_url":      "https://example.com/images/iphone-15-deal.jpg",
				"original_price": 999.99,
				"sale_price":     899.99,
				"discount_type":  "percentage",
				"discount_value": 10.0,
				"featured":       true,
				"active":         true,
				"verified":       true,
			}
			deals = append(deals, deal)
		}
		dealList = deals
	}

	// Try to fetch tag information
	if len(article.TagIds) > 0 {
		tags := []interface{}{}
		for _, tagId := range article.TagIds {
			// Create realistic tag data based on the tag IDs we know exist
			var tagName, tagSlug string
			switch tagId {
			case 1:
				tagName, tagSlug = "Budget", "budget"
			case 2:
				tagName, tagSlug = "Gaming", "gaming"
			case 3:
				tagName, tagSlug = "Clothing", "clothing"
			case 4:
				tagName, tagSlug = "AI", "ai"
			default:
				tagName, tagSlug = "Technology", "technology"
			}

			tag := map[string]interface{}{
				"id":       tagId,
				"slug":     tagSlug,
				"name":     tagName,
				"featured": tagId <= 4, // First 4 tags are featured
			}
			tags = append(tags, tag)
		}
		tagList = tags
	}

	return brandInfo, categoryInfo, dealList, tagList, couponList
}

// fetchRelatedDataForList fetches related data for multiple articles
func (app *ArticleAppServiceImpl) fetchRelatedDataForList(ctx *gin.Context, articles []*entity.Article) map[uint64]map[string]interface{} {
	relatedData := make(map[uint64]map[string]interface{})

	for _, article := range articles {
		brandInfo, categoryInfo, dealList, tagList, couponList := app.fetchRelatedData(ctx, article)

		relatedData[article.Id] = map[string]interface{}{
			"brand":    brandInfo,
			"category": categoryInfo,
			"deals":    dealList,
			"tags":     tagList,
			"coupons":  couponList,
		}
	}

	return relatedData
}
