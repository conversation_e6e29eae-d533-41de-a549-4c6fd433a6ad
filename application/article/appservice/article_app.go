package appservice

import (
	"brandreviews/application/article/dto"
	"brandreviews/domain/article/entity"
	"brandreviews/domain/article/service"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
)

type ArticleAppService interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
}

type ArticleAppServiceImpl struct {
	articleService service.ArticleService
}

func NewArticleAppService(
	articleService service.ArticleService,
) ArticleAppService {
	return &ArticleAppServiceImpl{
		articleService: articleService,
	}
}

func (app *ArticleAppServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.articleService.GetArticleCount(ctx)
}

func (app *ArticleAppServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailById(ctx, id)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoArticleDetailResp(article), nil
}

func (app *ArticleAppServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error) {
	article, err := app.articleService.GetArticleDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoArticleDetailResp(article), nil
}

func (app *ArticleAppServiceImpl) GetArticleListByCondition(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error) {
	condition := req.Dto2ConditionGetArticleList()
	articles, total, err := app.articleService.GetArticleListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return dto.Entity2DtoArticleListResp(req.Page, req.PageSize, total, articles), nil
}

func (app *ArticleAppServiceImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.CreateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return app.articleService.UpdateArticle(ctx, article)
}

func (app *ArticleAppServiceImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	return app.articleService.DeleteArticle(ctx, id)
}
