package dto

import (
	"brandreviews/domain/brand/entity"
)

type GetBrandListReq struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	PageSize   int    `form:"page_size,default=30" binding:"min=1,max=100"`
	Featured   *bool  `form:"featured"`
	Active     *bool  `form:"active"`
	CategoryId uint64 `form:"category_id"`
	Search     string `form:"search"`
}

type BrandDetailResp struct {
	Id           uint64 `json:"id"`
	Slug         string `json:"slug"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	Logo         string `json:"logo"`
	Website      string `json:"website"`
	Featured     bool   `json:"featured"`
	CategorySlug string `json:"category_slug"`
}

type BrandListResp struct {
	Total     int64              `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
	BrandList []*BrandDetailResp `json:"brand_list"`
}

func (req *GetBrandListReq) Dto2ConditionGetBrandList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Featured != nil {
		condition["featured"] = *req.Featured
	}
	if req.Active != nil {
		condition["active"] = *req.Active
	}
	if req.CategoryId > 0 {
		condition["category_id"] = req.CategoryId
	}
	return condition
}

func Entity2DtoBrandDetailResp(brand *entity.Brand) (resp *BrandDetailResp) {
	resp = &BrandDetailResp{}
	resp.Id = brand.Id
	resp.Slug = brand.Slug
	resp.Name = brand.Name
	resp.Description = brand.Description
	resp.Logo = brand.Logo
	resp.Website = brand.Website
	resp.Featured = brand.Featured
	resp.CategorySlug = brand.CategorySlug
	return resp
}

func Entity2DtoBrandListResp(pageSize int, page int, total int64, brandList []*entity.Brand) (resp *BrandListResp) {
	resp = &BrandListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range brandList {
		resp.BrandList = append(resp.BrandList, Entity2DtoBrandDetailResp(brandList[i]))
	}
	return resp
}
