package service

import (
	"net/url"
	"strings"
	"time"

	brandapp "brandreviews/application/brand/appservice"
	couponapp "brandreviews/application/coupon/appservice"
	"brandreviews/config"
	couponentity "brandreviews/domain/coupon/entity"
	"brandreviews/infra/ecode"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SyncService interface {
	SyncCouponList(ctx *gin.Context) *ecode.Error
}

type SyncServiceImpl struct {
	config        *config.Config
	logger        *zap.Logger
	couponService couponapp.CouponAppService
	brandService  brandapp.BrandAppService
}

func NewSyncService(
	config *config.Config,
	logger *zap.Logger,
	couponService couponapp.CouponAppService,
	brandService brandapp.BrandAppService,
) SyncService {
	return &SyncServiceImpl{
		config:        config,
		logger:        logger,
		couponService: couponService,
		brandService:  brandService,
	}
}

func (s *SyncServiceImpl) SyncCouponList(ctx *gin.Context) *ecode.Error {
	s.logger.Info("开始同步 coupon 信息")

	// 获取现有的优惠券
	existingCoupons, err := s.getAllExistingCoupons(ctx)
	if err != nil {
		s.logger.Error("获取现有优惠券失败", zap.Error(err))
		return err
	}

	// 获取现有的品牌信息
	existingBrands, err := s.getAllExistingBrands(ctx)
	if err != nil {
		s.logger.Error("获取现有品牌失败", zap.Error(err))
		return err
	}

	// 创建品牌域名映射，用于快速查找
	brandDomainMap := make(map[string]uint64)
	for _, brand := range existingBrands {
		if brand.Website != "" {
			domain := s.extractDomain(brand.Website)
			if domain != "" {
				brandDomainMap[domain] = brand.Id
			}
		}
	}

	// 创建coupon code映射，用于快速查找
	couponCodeMap := make(map[string]*couponentity.Coupon)
	for _, c := range existingCoupons {
		couponCodeMap[c.Code] = c
	}

	// 遍历配置中的优惠券账户
	for _, accountConfig := range s.config.Task.DataCollection.CouponsAccounts {
		platformType := accountConfig.Type
		var coupons []*couponentity.Coupon

		if platformType == "linkbux" {
			token := accountConfig.Token
			limit := accountConfig.Limit
			// 获取优惠券数据
			couponsData, err := s.fetchCouponsFromLinkBux(token, limit)
			if err != nil {
				s.logger.Error("linkbux获取优惠券数据失败", zap.Error(err))
				continue
			}
			coupons = s.toEntityCreateCoupon(couponsData, brandDomainMap)
		}

		// 创建或更新优惠券信息
		var createCoupons []*couponentity.Coupon
		uniqueCouponMap := make(map[string]bool)
		for _, coupon := range coupons {
			if _, exists := uniqueCouponMap[coupon.Code]; exists {
				continue
			}
			if _, exists := couponCodeMap[coupon.Code]; !exists {
				createCoupons = append(createCoupons, coupon)
			}
			uniqueCouponMap[coupon.Code] = true
		}

		// 批量创建新优惠券
		if len(createCoupons) > 0 {
			if err := s.batchCreateCoupons(ctx, createCoupons); err != nil {
				s.logger.Error("批量创建coupon失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			} else {
				s.logger.Info("成功创建优惠券",
					zap.String("platform_type", platformType),
					zap.Int("count", len(createCoupons)))
			}
		}
	}

	s.logger.Info("同步coupon信息完成")
	return nil
}

// getAllExistingCoupons 获取所有现有优惠券
func (s *SyncServiceImpl) getAllExistingCoupons(ctx *gin.Context) ([]*couponentity.Coupon, *ecode.Error) {
	// 这里需要实现获取所有优惠券的逻辑
	// 由于当前的CouponAppService没有GetAllCoupons方法，我们需要通过条件查询来获取
	// 暂时返回空列表，实际实现时需要添加相应的方法
	return []*couponentity.Coupon{}, nil
}

// getAllExistingBrands 获取所有现有品牌
func (s *SyncServiceImpl) getAllExistingBrands(ctx *gin.Context) ([]*BrandInfo, *ecode.Error) {
	// 这里需要实现获取所有品牌的逻辑
	// 由于当前的BrandAppService没有GetAllBrands方法，我们需要通过条件查询来获取
	// 暂时返回空列表，实际实现时需要添加相应的方法
	return []*BrandInfo{}, nil
}

// extractDomain 从URL中提取域名
func (s *SyncServiceImpl) extractDomain(website string) string {
	if website == "" {
		return ""
	}

	// 如果没有协议前缀，添加http://
	if !strings.HasPrefix(website, "http://") && !strings.HasPrefix(website, "https://") {
		website = "http://" + website
	}

	parsedURL, err := url.Parse(website)
	if err != nil {
		s.logger.Warn("解析URL失败", zap.String("website", website), zap.Error(err))
		return ""
	}

	domain := strings.ToLower(parsedURL.Hostname())
	// 移除www前缀
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return domain
}

// fetchCouponsFromLinkBux 从LinkBux获取优惠券数据
func (s *SyncServiceImpl) fetchCouponsFromLinkBux(token string, limit int) ([]map[string]interface{}, error) {
	// 这里需要实现实际的LinkBux API调用
	// 暂时返回模拟数据结构，实际实现时需要调用真实的API
	s.logger.Info("从LinkBux获取优惠券数据", zap.String("token", token), zap.Int("limit", limit))

	// 返回空数据，实际实现时需要调用LinkBux API
	return []map[string]interface{}{}, nil
}

// batchCreateCoupons 批量创建优惠券
func (s *SyncServiceImpl) batchCreateCoupons(ctx *gin.Context, coupons []*couponentity.Coupon) error {
	// 这里需要实现批量创建优惠券的逻辑
	// 由于当前的CouponAppService没有BatchCreate方法，我们需要逐个创建
	for _, coupon := range coupons {
		// 这里需要调用创建优惠券的方法
		// 暂时只记录日志，实际实现时需要调用真实的创建方法
		s.logger.Info("创建优惠券", zap.String("code", coupon.Code), zap.String("title", coupon.Title))
	}
	return nil
}

// toEntityCreateCoupon 将外部数据转换为优惠券实体
func (s *SyncServiceImpl) toEntityCreateCoupon(createDataRows []map[string]interface{}, brandDomainMap map[string]uint64) []*couponentity.Coupon {
	couponList := []*couponentity.Coupon{}

	for _, data := range createDataRows {
		coupon := &couponentity.Coupon{}

		// 根据域名关联品牌
		if domainInterface, ok := data["domain"]; ok {
			if domain, ok := domainInterface.(string); ok && domain != "" {
				domain = s.extractDomain(domain)
				if brandId, exists := brandDomainMap[domain]; exists {
					coupon.BrandId = brandId
				} else {
					// 如果找不到对应的品牌，跳过这个优惠券
					s.logger.Warn("找不到对应的品牌", zap.String("domain", domain))
					continue
				}
			} else {
				// 如果没有域名信息，跳过这个优惠券
				continue
			}
		} else {
			// 如果没有域名信息，跳过这个优惠券
			continue
		}

		// 验证优惠券代码
		if codeInterface, ok := data["coupon_code"]; ok {
			if code, ok := codeInterface.(string); ok && len(code) > 0 && code != "No Coupons Needed" {
				coupon.Code = code
				// 生成slug，使用code的小写版本
				coupon.Slug = strings.ToLower(strings.ReplaceAll(code, " ", "-"))
			} else {
				continue
			}
		} else {
			continue
		}

		// 设置优惠券信息
		if titleInterface, ok := data["coupon_title"]; ok {
			if title, ok := titleInterface.(string); ok {
				coupon.Title = title
			}
		}

		if descInterface, ok := data["description"]; ok {
			if desc, ok := descInterface.(string); ok {
				coupon.Description = desc
			}
		}

		// 设置折扣类型，默认为percentage
		coupon.DiscountType = "percentage"

		if discountInterface, ok := data["discount"]; ok {
			if discountStr, ok := discountInterface.(string); ok {
				// 这里可以解析折扣值，但需要根据实际的数据格式来处理
				// 暂时设置为0，实际实现时需要解析字符串中的数值
				_ = discountStr // 避免未使用变量警告
				coupon.DiscountValue = 0
			}
		}

		// 设置优惠券URL，如果没有提供则使用空字符串
		if urlInterface, ok := data["coupon_url"]; ok {
			if couponUrl, ok := urlInterface.(string); ok {
				coupon.CouponUrl = couponUrl
			}
		} else {
			// 如果没有URL，使用默认值
			coupon.CouponUrl = "https://example.com"
		}

		// 解析日期
		if startedAtInterface, ok := data["started_at"]; ok {
			if startedAtStr, ok := startedAtInterface.(string); ok {
				if startedAtDate, err := time.Parse("2006-01-02", startedAtStr); err == nil {
					coupon.StartDate = uint64(startedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("started_at", startedAtStr))
					continue
				}
			}
		}

		if endedAtInterface, ok := data["ended_at"]; ok {
			if endedAtStr, ok := endedAtInterface.(string); ok {
				if endedAtDate, err := time.Parse("2006-01-02", endedAtStr); err == nil {
					coupon.EndDate = uint64(endedAtDate.Unix())
				} else {
					s.logger.Warn("日期解析错误", zap.Error(err), zap.String("ended_at", endedAtStr))
					continue
				}
			}
		}

		// 设置默认值
		coupon.Active = true
		coupon.Featured = false
		coupon.Verified = false
		coupon.UsageLimit = 0 // 无限制
		coupon.UsedCount = 0
		coupon.UserUsageLimit = 1

		coupon.CreatedAt = time.Now()
		coupon.UpdatedAt = time.Now()
		couponList = append(couponList, coupon)
	}

	return couponList
}

// BrandInfo 临时定义，用于品牌信息
type BrandInfo struct {
	Id      uint64 `json:"id"`
	Website string `json:"website"`
}
