package scheduler

import (
	"context"
	"time"

	"brandreviews/application/task/service"
	"brandreviews/config"

	"github.com/gin-gonic/gin"
	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

type Scheduler interface {
	Start(ctx context.Context) error
	Stop() error
	IsRunning() bool
}

type SchedulerImpl struct {
	config      *config.Config
	logger      *zap.Logger
	syncService service.SyncService
	scheduler   gocron.Scheduler
	isRunning   bool
}

func NewScheduler(
	config *config.Config,
	logger *zap.Logger,
	syncService service.SyncService,
) (Scheduler, error) {
	// 设置时区
	location, err := time.LoadLocation(config.Task.Scheduler.Timezone)
	if err != nil {
		logger.Warn("无法加载时区，使用默认时区", 
			zap.String("timezone", config.Task.Scheduler.Timezone), 
			zap.Error(err))
		location = time.Local
	}

	// 创建调度器
	s, err := gocron.NewScheduler(gocron.WithLocation(location))
	if err != nil {
		return nil, err
	}

	return &SchedulerImpl{
		config:      config,
		logger:      logger,
		syncService: syncService,
		scheduler:   s,
		isRunning:   false,
	}, nil
}

func (s *SchedulerImpl) Start(ctx context.Context) error {
	if !s.config.Task.Scheduler.Enabled {
		s.logger.Info("定时任务调度器已禁用")
		return nil
	}

	s.logger.Info("启动定时任务调度器")

	// 添加优惠券同步任务 - 每小时执行一次
	_, err := s.scheduler.NewJob(
		gocron.DurationJob(1*time.Hour),
		gocron.NewTask(s.syncCoupons),
		gocron.WithName("sync_coupons"),
	)
	if err != nil {
		s.logger.Error("创建优惠券同步任务失败", zap.Error(err))
		return err
	}

	// 添加立即执行一次的任务（用于测试）
	_, err = s.scheduler.NewJob(
		gocron.OneTimeJob(gocron.OneTimeJobStartImmediately()),
		gocron.NewTask(s.syncCoupons),
		gocron.WithName("sync_coupons_immediate"),
	)
	if err != nil {
		s.logger.Error("创建立即执行的优惠券同步任务失败", zap.Error(err))
		return err
	}

	// 启动调度器
	s.scheduler.Start()
	s.isRunning = true

	s.logger.Info("定时任务调度器启动成功")
	return nil
}

func (s *SchedulerImpl) Stop() error {
	if !s.isRunning {
		return nil
	}

	s.logger.Info("停止定时任务调度器")
	
	// 停止调度器
	err := s.scheduler.Shutdown()
	if err != nil {
		s.logger.Error("停止调度器失败", zap.Error(err))
		return err
	}

	s.isRunning = false
	s.logger.Info("定时任务调度器已停止")
	return nil
}

func (s *SchedulerImpl) IsRunning() bool {
	return s.isRunning
}

// syncCoupons 同步优惠券数据的任务函数
func (s *SchedulerImpl) syncCoupons() {
	s.logger.Info("开始执行优惠券同步任务")
	
	// 创建一个新的gin.Context用于任务执行
	// 注意：在实际生产环境中，可能需要使用不同的上下文管理方式
	ctx := &gin.Context{}
	
	err := s.syncService.SyncCouponList(ctx)
	if err != nil {
		s.logger.Error("优惠券同步任务执行失败", zap.Error(err))
	} else {
		s.logger.Info("优惠券同步任务执行成功")
	}
}
