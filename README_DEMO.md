# 🚀 Brand Reviews API - 演示指南

## 概述

Brand Reviews API 是一个高性能的品牌评测和优惠信息管理系统，具备以下特性：

- ⚡ **高性能**: 多级缓存、内存优化、压缩传输
- 🛡️ **高可靠**: 熔断器、限流、优雅关闭
- 📊 **可观测**: 健康检查、指标监控、请求追踪
- 🔒 **高安全**: 输入验证、XSS防护、错误处理
- 📚 **易使用**: RESTful设计、完整文档、示例数据

## 🎯 演示内容

本演示将展示：

1. **完整的API功能** - 品牌、优惠、优惠券管理
2. **性能优化效果** - 缓存加速、并发处理
3. **监控和健康检查** - 系统状态、指标收集
4. **错误处理机制** - 优雅的错误响应
5. **高级查询功能** - 搜索、筛选、分页

## 🛠️ 快速开始

### 1. 环境准备

确保已安装以下软件：

```bash
# Go 1.21+
go version

# PostgreSQL 13+
psql --version

# 可选：jq (用于JSON格式化)
jq --version
```

### 2. 克隆项目

```bash
git clone <repository-url>
cd brandreviews
```

### 3. 配置环境

```bash
# 复制配置文件
cp config.example.yaml config.yaml

# 根据实际环境修改配置
vim config.yaml
```

### 4. 初始化数据库

```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 初始化数据库和插入测试数据
./scripts/init_database.sh
```

### 5. 启动服务

```bash
# 安装依赖
go mod tidy

# 启动API服务
go run cmd/api/main.go
```

服务启动后，你应该看到类似输出：
```
2024/01/15 10:30:00 INFO Starting HTTP server addr=:8080
2024/01/15 10:30:00 INFO Server started successfully addr=:8080
```

### 6. 运行演示

在新的终端窗口中运行：

```bash
# 运行完整的API演示
./scripts/demo_api_results.sh

# 或运行基础测试
./scripts/test_api.sh
```

## 📊 演示数据

系统包含以下测试数据：

### 品牌数据 (8个品牌)
- **Apple** - 苹果公司 (电子产品)
- **Samsung** - 三星电子 (电子产品)
- **Sony** - 索尼公司 (电子产品)
- **Nike** - 耐克 (时尚服装)
- **Adidas** - 阿迪达斯 (时尚服装)
- **Zara** - Zara (时尚服装)
- **L'Oréal** - 欧莱雅 (美容护肤)
- **Estée Lauder** - 雅诗兰黛 (美容护肤)

### 优惠活动 (6个活动)
- iPhone 15 限时优惠 (立省1000元)
- MacBook Air 春季促销 (11.11%折扣)
- Galaxy S24 新品促销 (立省1000元)
- Nike 春季大促 (33.37%折扣)
- Air Jordan 限量折扣 (立省300元)
- L'Oréal 护肤品特惠 (33.44%折扣)

### 优惠券 (6张优惠券)
- Apple 新用户专享券 (500元)
- Apple VIP 会员券 (8折)
- Samsung 春季优惠券 (300元)
- Nike 会员专享券 (15%折扣)
- Nike 免费配送券
- L'Oréal 美妆优惠券 (25%折扣)

### 文章内容 (5篇文章)
- iPhone 15 深度评测
- Samsung vs Apple 旗舰对决
- 2024春季运动时尚趋势
- 2024年护肤新趋势
- 3月最佳优惠汇总

## 🎮 API 演示示例

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8080/health

# 详细健康检查
curl http://localhost:8080/health/detailed
```

### 品牌管理

```bash
# 获取所有品牌
curl http://localhost:8080/api/v1/brand

# 搜索品牌
curl "http://localhost:8080/api/v1/brand?search=Apple"

# 获取特色品牌
curl "http://localhost:8080/api/v1/brand?featured=true"

# 根据ID获取品牌
curl http://localhost:8080/api/v1/brand/1

# 根据slug获取品牌
curl http://localhost:8080/api/v1/brand/slug/apple
```

### 优惠活动

```bash
# 获取所有优惠
curl http://localhost:8080/api/v1/deal

# 搜索优惠
curl "http://localhost:8080/api/v1/deal?search=iPhone"

# 获取有效优惠
curl "http://localhost:8080/api/v1/deal?valid_only=true"

# 按品牌筛选
curl "http://localhost:8080/api/v1/deal?brand_id=1"
```

### 优惠券管理

```bash
# 获取所有优惠券
curl http://localhost:8080/api/v1/coupon

# 根据优惠码查询
curl http://localhost:8080/api/v1/coupon/code/APPLE500

# 获取可用优惠券
curl "http://localhost:8080/api/v1/coupon?available_only=true"
```

## ⚡ 性能演示

### 缓存效果测试

```bash
# 第一次请求 (缓存未命中)
time curl -s http://localhost:8080/api/v1/brand/1

# 第二次请求 (缓存命中，明显更快)
time curl -s http://localhost:8080/api/v1/brand/1
```

### 并发处理测试

```bash
# 使用 Apache Bench 测试并发
ab -n 100 -c 10 http://localhost:8080/api/v1/brand

# 或使用 curl 并发测试
for i in {1..10}; do
    curl -s http://localhost:8080/api/v1/brand?page=$i &
done
wait
```

### 限流测试

```bash
# 快速发送多个请求触发限流
for i in {1..150}; do
    curl -s -w "%{http_code}\n" http://localhost:8080/api/v1/brand -o /dev/null
done
```

## 📈 预期性能指标

### 响应时间
- **缓存命中**: < 10ms
- **缓存未命中**: 50-200ms
- **复杂查询**: 200-500ms

### 吞吐量
- **缓存请求**: 10,000+ RPS
- **数据库查询**: 1,000+ RPS
- **并发连接**: 支持数千并发

### 缓存效果
- **命中率**: 80-90%
- **内存使用**: 优化后减少20-30%
- **响应时间**: 提升40-60%

## 🔍 监控和调试

### 查看日志

```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

### 监控指标

```bash
# 系统指标
curl http://localhost:8080/metrics

# Prometheus格式指标
curl http://localhost:8080/metrics/prometheus

# 缓存统计
curl http://localhost:8080/metrics/cache
```

### 数据库状态

```bash
# 连接数据库查看数据
psql -h localhost -U postgres -d brandreviews

# 查看表数据
\dt
SELECT COUNT(*) FROM brands;
SELECT COUNT(*) FROM deals;
SELECT COUNT(*) FROM coupons;
```

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :8080
   
   # 检查配置文件
   cat config.yaml
   ```

2. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   pg_isready -h localhost -p 5432
   
   # 检查数据库是否存在
   psql -h localhost -U postgres -l
   ```

3. **缓存问题**
   ```bash
   # 清理缓存
   curl -X POST http://localhost:8080/admin/cache/clear
   ```

### 重置环境

```bash
# 重新初始化数据库
./scripts/init_database.sh

# 重启服务
pkill -f "go run cmd/api/main.go"
go run cmd/api/main.go
```

## 📚 相关文档

- [完整API文档](docs/API_DOCUMENTATION.md)
- [数据库迁移脚本](migrations/001_optimize_entities.sql)
- [配置文件说明](config.example.yaml)

## 🎉 演示总结

通过本演示，你将看到：

✅ **完整的API生态系统** - 从健康检查到业务功能
✅ **卓越的性能表现** - 缓存加速和并发处理
✅ **企业级的可靠性** - 错误处理和监控
✅ **开发者友好** - 清晰的文档和示例
✅ **生产就绪** - 完整的部署和运维支持

这是一个真正的企业级API系统，具备了生产环境所需的所有特性！
