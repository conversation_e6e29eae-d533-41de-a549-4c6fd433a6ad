{"level":"info","ts":"2025-04-27T18:11:34.795+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-04-27T19:06:46.123+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-27T19:06:46.127+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-04-28T11:00:03.747+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-28T11:00:03.750+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-04-28T14:25:38.185+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-04-28T14:41:16.462+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-28T14:41:16.468+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-04-28T14:41:27.499+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-04-28T14:47:01.061+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-28T14:47:01.065+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-04-28T14:47:56.175+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-04-29T02:40:42.903+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-29T02:40:42.905+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-04-29T13:25:00.794+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-04-29T21:47:29.794+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-04-29T21:47:29.803+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-06-03T16:08:55.477+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-06-03T16:15:34.335+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-06-03T16:15:34.337+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-06-03T17:52:43.238+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"error","ts":"2025-06-03T17:54:11.303+0800","caller":"dao/brand_dao.go:163","msg":"Failed to count brands","error":"ERROR: relation \"brands\" does not exist (SQLSTATE 42P01)","condition":{"offset":0,"page":1,"page_size":30},"stacktrace":"brandreviews/infra/dao.(*BrandPostgresRepository).GetBrandListByCondition\n\t/Users/<USER>/projects/brandreviews/infra/dao/brand_dao.go:163\nbrandreviews/domain/brand/service.(*BrandServiceImpl).GetBrandListByCondition\n\t/Users/<USER>/projects/brandreviews/domain/brand/service/brand_service.go:95\nbrandreviews/application/brand/appservice.(*BrandAppServiceImpl).GetBrandListByCondition\n\t/Users/<USER>/projects/brandreviews/application/brand/appservice/brand_app.go:51\nbrandreviews/interfaces/api/handler/brand.(*BrandHandler).GetBrandList\n\t/Users/<USER>/projects/brandreviews/interfaces/api/handler/brand/brand_handler.go:74\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\nbrandreviews/interfaces/api/router.(*ApiServer).setupPublicRoutes.CORS.func1\n\t/Users/<USER>/projects/brandreviews/interfaces/api/middleware/cors.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092"}
{"level":"error","ts":"2025-06-03T17:54:11.308+0800","caller":"service/brand_service.go:97","msg":"Failed to get Brands from database","condition":{"offset":0,"page":1,"page_size":30},"error":"failed to count brands: ERROR: relation \"brands\" does not exist (SQLSTATE 42P01)","stacktrace":"brandreviews/domain/brand/service.(*BrandServiceImpl).GetBrandListByCondition\n\t/Users/<USER>/projects/brandreviews/domain/brand/service/brand_service.go:97\nbrandreviews/application/brand/appservice.(*BrandAppServiceImpl).GetBrandListByCondition\n\t/Users/<USER>/projects/brandreviews/application/brand/appservice/brand_app.go:51\nbrandreviews/interfaces/api/handler/brand.(*BrandHandler).GetBrandList\n\t/Users/<USER>/projects/brandreviews/interfaces/api/handler/brand/brand_handler.go:74\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\nbrandreviews/interfaces/api/router.(*ApiServer).setupPublicRoutes.CORS.func1\n\t/Users/<USER>/projects/brandreviews/interfaces/api/middleware/cors.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/recovery.go:102\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/logger.go:249\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092"}
{"level":"info","ts":"2025-06-03T18:12:59.709+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"error","ts":"2025-06-03T18:12:59.710+0800","caller":"api/main.go:98","msg":"Server error","error":"listen tcp 0.0.0.0:9091: bind: address already in use","stacktrace":"main.main.func1\n\t/Users/<USER>/projects/brandreviews/cmd/api/main.go:98"}
{"level":"info","ts":"2025-06-03T18:13:07.727+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-06-03T18:13:07.728+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-06-03T18:14:36.719+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"error","ts":"2025-06-03T18:14:36.719+0800","caller":"api/main.go:98","msg":"Server error","error":"listen tcp 0.0.0.0:9091: bind: address already in use","stacktrace":"main.main.func1\n\t/Users/<USER>/projects/brandreviews/cmd/api/main.go:98"}
{"level":"info","ts":"2025-06-03T18:14:38.990+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-06-03T18:14:38.990+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-06-03T18:16:03.919+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-06-03T18:16:03.932+0800","caller":"api/main.go:116","msg":"Server exiting"}
{"level":"info","ts":"2025-06-03T18:19:14.786+0800","caller":"api/main.go:96","msg":"Starting API server","addr":"0.0.0.0:9091"}
{"level":"info","ts":"2025-06-03T18:29:15.704+0800","caller":"api/main.go:105","msg":"Received shutdown signal"}
{"level":"info","ts":"2025-06-03T18:29:15.707+0800","caller":"api/main.go:116","msg":"Server exiting"}
