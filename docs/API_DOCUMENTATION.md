# Brand Reviews API Documentation

## Overview

The Brand Reviews API provides comprehensive access to brands, deals, coupons, articles, categories, and tags. This RESTful API is built with Go and includes advanced features like caching, rate limiting, request tracing, and circuit breaker protection.

## Base URL

```
http://localhost:8080/api/v1
```

## Authentication

Currently, the API is publicly accessible. Authentication will be added in future versions.

## Rate Limiting

- **Default Limit**: 100 requests per second per IP
- **Burst Limit**: 200 requests
- **Headers**: Rate limit information is included in response headers:
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Request Tracing

All requests include a unique trace ID for debugging and monitoring:
- **Header**: `X-Trace-ID` is included in all responses
- **Logging**: All requests are logged with trace ID for correlation

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "code": 400,
  "message": "Invalid parameter",
  "data": null
}
```

### Validation Error Response
```json
{
  "code": 400,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "name",
        "tag": "required",
        "value": "",
        "message": "name is required"
      }
    ]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 0    | Success |
| 400  | Bad Request / Invalid Parameter |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 405  | Method Not Allowed |
| 409  | Conflict |
| 422  | Unprocessable Entity |
| 429  | Too Many Requests |
| 500  | Internal Server Error |
| 502  | Bad Gateway |
| 503  | Service Unavailable |
| 504  | Gateway Timeout |

## Pagination

List endpoints support pagination with the following parameters:

| Parameter | Type | Default | Max | Description |
|-----------|------|---------|-----|-------------|
| `page` | integer | 1 | - | Page number (1-based) |
| `page_size` | integer | 30 | 100 | Number of items per page |

### Pagination Response
```json
{
  "total": 150,
  "page": 1,
  "page_size": 30,
  "brand_list": [...]
}
```

## Filtering and Sorting

### Common Filters

| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search in name/title and description |
| `featured` | boolean | Filter by featured status |
| `active` | boolean | Filter by active status |
| `category_id` | integer | Filter by category ID |

### Deal/Coupon Specific Filters

| Parameter | Type | Description |
|-----------|------|-------------|
| `verified` | boolean | Filter by verified status |
| `valid_only` | boolean | Filter to show only currently valid items |
| `brand_id` | integer | Filter by brand ID |

### Coupon Specific Filters

| Parameter | Type | Description |
|-----------|------|-------------|
| `available_only` | boolean | Filter to show only available coupons |
| `discount_type` | string | Filter by discount type (percentage, fixed, free_shipping) |

## Caching

The API implements multi-level caching for optimal performance:

- **L1 Cache**: 5-minute TTL for frequently accessed data
- **L2 Cache**: 1-hour TTL for moderately accessed data
- **Cache Headers**: Responses may include cache-related headers

## Compression

Responses are automatically compressed using gzip when:
- Client accepts gzip encoding (`Accept-Encoding: gzip`)
- Response size is >= 1KB
- Content type is compressible

## Health Checks

### Basic Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "UP",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "uptime": "2h30m15s",
  "components": {
    "database": {"status": "UP"},
    "cache": {"status": "UP"},
    "memory": {"status": "UP"}
  }
}
```

### Detailed Health Check
```http
GET /health/detailed
```

Includes detailed metrics and performance data.

### Readiness Check
```http
GET /health/ready
```

Returns 200 if the service is ready to handle requests.

### Liveness Check
```http
GET /health/live
```

Returns 200 if the service is alive.

## API Endpoints

### Articles

#### List Articles
```http
GET /api/v1/article
```

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 10, max: 100)
- `search` (string): Search in article title and content
- `category` (string): Filter by category slug
- `brand` (string): Filter by brand slug
- `tag` (string): Filter by tag slug

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 5,
    "page": 1,
    "page_size": 10,
    "article_list": [
      {
        "id": 1,
        "slug": "iphone-15-review",
        "title": "iPhone 15 深度评测：值得升级吗？",
        "description": "iPhone 15 系列全面评测，从性能到拍照功能详细分析",
        "content": "iPhone 15 作为苹果2023年的旗舰产品...",
        "featured_image": "https://example.com/articles/iphone15-review.jpg",
        "published_at": "2025-05-29T18:09:13+08:00",
        "category_info": "1",
        "deal_list": [1, 2],
        "tag_list": [1, 2, 3],
        "brand_info": 1,
        "created_at": "2025-05-29T18:09:12.581927+08:00",
        "updated_at": "2025-06-03T18:09:12.581927+08:00"
      }
    ]
  }
}
```

#### Get Article by ID
```http
GET /api/v1/article/{id}
```

**Path Parameters:**
- `id` (integer): Article ID

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "slug": "iphone-15-review",
    "title": "iPhone 15 深度评测：值得升级吗？",
    "description": "iPhone 15 系列全面评测，从性能到拍照功能详细分析",
    "content": "iPhone 15 作为苹果2023年的旗舰产品...",
    "featured_image": "https://example.com/articles/iphone15-review.jpg",
    "published_at": "2025-05-29T18:09:13+08:00",
    "category_info": "1",
    "deal_list": [1, 2],
    "tag_list": [1, 2, 3],
    "brand_info": 1,
    "created_at": "2025-05-29T18:09:12.581927+08:00",
    "updated_at": "2025-06-03T18:09:12.581927+08:00"
  }
}
```

#### Get Article by Slug
```http
GET /api/v1/article/slug/{slug}
```

**Path Parameters:**
- `slug` (string): Article slug (SEO-friendly URL identifier)

**Response:** Same as Get Article by ID

### Brands

#### List Brands
```http
GET /api/v1/brand
```

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 30, max: 100)
- `search` (string): Search in brand name and description
- `featured` (boolean): Filter by featured status
- `active` (boolean): Filter by active status
- `category_id` (integer): Filter by category ID

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 30,
    "brand_list": [
      {
        "id": 1,
        "slug": "example-brand",
        "name": "Example Brand",
        "description": "A great brand for examples",
        "logo": "https://example.com/logo.png",
        "website": "https://example.com",
        "affiliate_link": "https://affiliate.example.com",
        "featured": true,
        "active": true,
        "category_id": 1,
        "category_slug": "electronics"
      }
    ]
  }
}
```

#### Get Brand by ID
```http
GET /api/v1/brand/{id}
```

**Path Parameters:**
- `id` (integer): Brand ID

#### Get Brand by Slug
```http
GET /api/v1/brand/slug/{slug}
```

**Path Parameters:**
- `slug` (string): Brand slug

### Deals

#### List Deals
```http
GET /api/v1/deal
```

**Query Parameters:**
- `page` (integer): Page number
- `page_size` (integer): Items per page
- `search` (string): Search in deal title and description
- `featured` (boolean): Filter by featured status
- `active` (boolean): Filter by active status
- `verified` (boolean): Filter by verified status
- `valid_only` (boolean): Show only currently valid deals
- `brand_id` (integer): Filter by brand ID
- `category_id` (integer): Filter by category ID

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 25,
    "page": 1,
    "page_size": 30,
    "deal_list": [
      {
        "id": 1,
        "slug": "great-deal",
        "title": "50% Off Electronics",
        "description": "Amazing discount on all electronics",
        "deal_url": "https://example.com/deal",
        "image_url": "https://example.com/deal-image.jpg",
        "original_price": 100.00,
        "sale_price": 50.00,
        "discount_type": "percentage",
        "discount_value": 50.00,
        "featured": true,
        "active": true,
        "verified": true,
        "start_date": 1705123200,
        "end_date": 1705209600,
        "brand_id": 1,
        "brand_slug": "example-brand",
        "category_id": 1,
        "category_slug": "electronics"
      }
    ]
  }
}
```

#### Get Deal by ID
```http
GET /api/v1/deal/{id}
```

#### Get Deal by Slug
```http
GET /api/v1/deal/slug/{slug}
```

### Coupons

#### List Coupons
```http
GET /api/v1/coupon
```

**Query Parameters:**
- `page` (integer): Page number
- `page_size` (integer): Items per page
- `search` (string): Search in coupon title, description, and code
- `featured` (boolean): Filter by featured status
- `active` (boolean): Filter by active status
- `verified` (boolean): Filter by verified status
- `valid_only` (boolean): Show only currently valid coupons
- `available_only` (boolean): Show only available coupons (not at usage limit)
- `brand_id` (integer): Filter by brand ID
- `category_id` (integer): Filter by category ID
- `discount_type` (string): Filter by discount type

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 15,
    "page": 1,
    "page_size": 30,
    "coupon_list": [
      {
        "id": 1,
        "slug": "save20-coupon",
        "title": "Save 20% on Everything",
        "description": "Get 20% off your entire order",
        "code": "SAVE20",
        "coupon_url": "https://example.com/coupon",
        "discount_type": "percentage",
        "discount_value": 20.00,
        "min_order_value": 50.00,
        "max_discount": 100.00,
        "featured": true,
        "active": true,
        "verified": true,
        "start_date": 1705123200,
        "end_date": 1705209600,
        "usage_limit": 1000,
        "used_count": 150,
        "user_usage_limit": 1,
        "brand_id": 1,
        "brand_slug": "example-brand",
        "category_id": 1,
        "category_slug": "electronics"
      }
    ]
  }
}
```

#### Get Coupon by ID
```http
GET /api/v1/coupon/{id}
```

#### Get Coupon by Slug
```http
GET /api/v1/coupon/slug/{slug}
```

#### Get Coupon by Code
```http
GET /api/v1/coupon/code/{code}
```

**Path Parameters:**
- `code` (string): Coupon code

### Categories

#### List Categories
```http
GET /api/v1/category
```

### Tags

#### List Tags
```http
GET /api/v1/tag
```

## Performance Characteristics

### Response Times (with caching)
- **Cache Hit**: < 10ms
- **Cache Miss**: 50-200ms
- **Complex Queries**: 200-500ms

### Throughput
- **Cached Requests**: 10,000+ RPS
- **Database Queries**: 1,000+ RPS
- **Complex Aggregations**: 100+ RPS

### SLA Expectations
- **Availability**: 99.9%
- **Response Time P95**: < 500ms
- **Response Time P99**: < 1s

## Circuit Breaker

The API implements circuit breaker pattern for resilience:
- **Failure Threshold**: 5 failures in 60 seconds
- **Open State Duration**: 60 seconds
- **Half-Open Max Requests**: 10

When circuit breaker is open, requests return:
```json
{
  "code": 503,
  "message": "Service temporarily unavailable"
}
```

## Monitoring and Metrics

### Request Metrics
- Total requests
- Error rate
- Response times (avg, p50, p95, p99)
- Active requests
- Requests by method/path/status

### Cache Metrics
- Hit ratio
- Cache size
- Evictions
- Set/get operations

### Health Metrics
- Database connection status
- Memory usage
- Goroutine count
- Uptime

## Best Practices

### Client Implementation
1. **Implement retry logic** with exponential backoff
2. **Handle rate limiting** by respecting rate limit headers
3. **Use appropriate timeouts** (recommended: 30 seconds)
4. **Cache responses** when appropriate
5. **Include User-Agent** header for identification

### Error Handling
1. **Check response code** before processing data
2. **Log trace IDs** for debugging
3. **Implement fallback mechanisms** for critical operations
4. **Monitor error rates** and patterns

### Performance Optimization
1. **Use pagination** for large datasets
2. **Implement client-side caching** for static data
3. **Batch requests** when possible
4. **Use appropriate filters** to reduce response size

## Support

For API support and questions:
- **Documentation**: This document
- **Health Checks**: Use `/health` endpoints for monitoring
- **Trace IDs**: Include trace ID in support requests for faster debugging
